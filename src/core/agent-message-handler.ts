/**
 * @description:
 * @Author: liuzhengzheng
 * @Date: 2025-03-22 19:37:08
 * @LastEditors: liuzhengzheng
 * @LastEditTime: 2025-03-24 15:28:22
 */
import { I<PERSON><PERSON><PERSON>er, IdeCommonMessage } from '@/protocol/messenger';
import { FromCoreProtocol, ToCoreProtocol } from '@/protocol';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import { AgentManager } from '@/agent/AgentManager';
import { CheckpointService } from '@/agent/services/CheckpointService';
import { StateManager } from '@/agent/services/StateManager';
import { MessageService } from '@/agent/services/MessageService';
import { LoggerManager } from '@/agent/services/LoggerManager';

/**
 * 处理助手代理相关消息的类
 */
export class AgentMessageHandler {
  private logger = new Logger('AgentMessageHandler');
  private _agentManager?: AgentManager;

  constructor(private readonly messenger: I<PERSON><PERSON><PERSON><PERSON><ToCoreProtocol, FromCoreProtocol>) { }

  /**
   * 处理助手代理相关的消息
   * @param msg 消息对象
   */
  public async handleAgentMessage(msg: any): Promise<void> {
    this.logger.info(`assistant/agent/local, ${JSON.stringify(msg)}`);
    this.logger.perf({
      namespace: ASSISTANT_NAMESPACE,
      subtag: 'assistant/agent/local',
      extra3: GlobalConfig.getConfig().getPlatform(),
      extra4: (msg.common as IdeCommonMessage)?.cwd,
      extra6: msg.data.type
    });

    const data = msg.data;
    const toolSwitchConfig = data.toolSwitchState;
    const cwd = (msg.common as IdeCommonMessage)?.cwd || process.cwd();
    // const messageId = msg.messageId;

    switch (data.type) {
      // 如果是新任务开始，删除旧实例，新建一个实例
      case 'newTask': {
        this._agentManager = undefined;
        this._agentManager = await AgentManager.init(this.messenger, cwd, data, toolSwitchConfig);
        this._agentManager.startTask();
        break;
      }
      case 'stop': {
        this._agentManager?.stop();
        break;
      }
      case 'askResponse': {
        this._agentManager?.updateAskResponse({
          askResponse: data.askResponse,
          text: data.text
        });
        break;
      }
      case 'restore': {
        // 创建基本的StateManager实例用于恢复检查点
        const stateManager = new StateManager({
          sessionId: data.params.sessionId,
          chatId: data.params.chatId,
          cwd: cwd
        });
        // 初始化日志管理器
        const loggerManager = new LoggerManager({
          sessionId: data.params.sessionId,
          chatId: data.params.chatId,
          username: GlobalConfig.getConfig().getUsername(),
          scope: 'assistant-agent'
        });
        // 创建MessageService实例
        const messageService = new MessageService(this.messenger, stateManager, loggerManager);
        // 创建CheckpointService实例并直接调用restoreCheckpoint
        const checkpointService = new CheckpointService(stateManager, messageService, loggerManager);
        await checkpointService.restoreCheckpoint(data.params.restoreCommitHash);
        break;
      }
      default:
        break;
    }
  }

  /**
   * 获取当前的代理管理器实例
   */
  public get agentManager(): AgentManager | undefined {
    return this._agentManager;
  }
}
