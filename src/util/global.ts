import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/protocol/messenger';
import type { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { AGENT_VERSION } from './const';
import type { IdeSettings, RepoInfo } from '@/protocol/index.d';
import { getDeviceInfo, type DeviceIdentifier } from './device-id';

export class GlobalConfig {
  static globalConfig: GlobalConfig;
  private username: string = '';
  private repoPath: string = '';
  private pluginVersion: string = '';
  private ideVersion: string = '';
  private agentVersion: string = AGENT_VERSION;
  private platform: string = '';
  private device: string = '';
  private deviceId: string = '';
  private ideSetting: IdeSettings | undefined;
  private KwaiPilotDomain: string = 'https://kwaipilot.corp.kuaishou.com/';
  private maxIndexSpace: number = 10; // 10GB
  private repoInfo: RepoInfo | undefined;
  private cwd: string = process.cwd();
  private isDBImgrated: boolean = false;
  constructor(private messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {}
  static init(messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>) {
    if (GlobalConfig.globalConfig) {
      return GlobalConfig.globalConfig;
    }
    GlobalConfig.globalConfig = new GlobalConfig(messenger);
    return GlobalConfig.globalConfig;
  }
  static getConfig() {
    if (!GlobalConfig.globalConfig) {
      throw new Error('GlobalConfig is not initialized, call init first');
    }
    return GlobalConfig.globalConfig;
  }
  async initData() {
    const [{ data: ideInfo }, { data: ideSetting }, deviceIdentifier] = await Promise.all([
      this.messenger.request('state/ideInfo', undefined),
      this.messenger.request('config/getIdeSetting', undefined),
      getDeviceInfo()
    ]);

    this.username = ideInfo?.userInfo?.name ?? '';
    this.repoPath = ideInfo?.repoInfo?.dir_path ?? ideSetting?.dirPath ?? '';
    this.repoInfo = ideInfo?.repoInfo ?? undefined;
    this.cwd = ideInfo?.cwd || this.repoPath || process.cwd();
    this.pluginVersion = ideInfo?.pluginVersion ?? '';
    this.ideVersion = ideInfo?.version ?? '';
    this.platform = ideInfo?.platform ?? '';
    // 优先使用IDE传递的device信息，如果没有则使用自动获取的设备标识
    this.device = ideInfo?.device ?? '';
    this.deviceId = `${deviceIdentifier.type}-${deviceIdentifier.value}`;
    this.KwaiPilotDomain = ideInfo?.proxyUrl ?? 'https://kwaipilot.corp.kuaishou.com/';
    this.maxIndexSpace = ideInfo?.maxIndexSpace ?? this.maxIndexSpace; // 10GB
    this.ideSetting = ideSetting;
    return GlobalConfig.getConfig();
  }
  setIsDBImgrated(isImgrated: boolean) {
    this.isDBImgrated = isImgrated;
  }

  getIsDBImgrated() {
    return this.isDBImgrated;
  }
  setUsername(username: string) {
    if (!username) {
      this.initData();
    } else {
      this.username = username;
    }
  }
  getUsername() {
    return this.username || 'unknown';
  }
  getRepoPath() {
    return this.repoPath;
  }
  getCwd() {
    return this.cwd;
  }
  getPluginVersion() {
    return this.pluginVersion;
  }
  getIdeVersion() {
    return this.ideVersion;
  }
  getAgentVersion() {
    return this.agentVersion;
  }
  getPlatform() {
    return this.platform;
  }
  getDevice() {
    return this.device;
  }
  getKwaiPilotDomain() {
    return this.KwaiPilotDomain.endsWith('/') ? this.KwaiPilotDomain : this.KwaiPilotDomain + '/';
  }
  async getMaxIndexSpace() {
    const { data: ideInfo } = await this.messenger.request('state/ideInfo', undefined);
    return (ideInfo?.maxIndexSpace ?? this.maxIndexSpace) * 1024 * 1024 * 1024;
  }
  getIdeSetting() {
    return this.ideSetting;
  }
  getRepoInfo() {
    return this.repoInfo;
  }
  getDeviceId() {
    return this.deviceId;
  }
}
