import { LocalMessage, MessageParam, MessageParamVersion0, TextBlockParamVersion1, ImageBlockParam } from '../types/type.d';
import { AssistantMessageContent } from '../types/message';
import { ToolSwitchManager } from './ToolSwitchManager';
import { ToolSwitchState } from '../utils/toolSwitches';

type AskResponse = 'yesButtonClicked' | 'noButtonClicked' | 'messageResponse';
type UserContent = (TextBlockParamVersion1 | ImageBlockParam)[];

export interface AgentState {
  // 基础状态
  sessionId: string;
  chatId: string;
  cwd: string;

  // 任务状态
  didCompleteTask: boolean;
  startTaskTime: number;
  startLlmTime: number;
  abort: boolean;
  abandoned: boolean;

  // 对话历史
  apiConversationHistory: (MessageParam | MessageParamVersion0)[];
  localMessages: LocalMessage[];
  conversationHistoryDeletedRange?: [number, number];

  // 流式处理状态
  isWaitingForFirstChunk: boolean;
  isStreaming: boolean;
  currentStreamingContentIndex: number;
  assistantMessageContent: AssistantMessageContent[];
  presentAssistantMessageLocked: boolean;
  presentAssistantMessageHasPendingUpdates: boolean;
  userMessageContent: TextBlockParamVersion1[];
  userMessageContentReady: boolean;
  didCompleteReadingStream: boolean;

  // 用户交互状态
  askResponse?: AskResponse;
  askResponseText?: string;
  didRejectTool: boolean;
  didAlreadyUseTool: boolean;

  // 错误处理状态
  consecutiveMistakeCount: number;
  isTooLongForApiReq: boolean;
  tooLongTip: string;
  didFinishAbortingStream: boolean;

  // 模型配置
  modelConfig: {
    model: string;
    maxTokens: number;
  };

  // 检查点状态
  lastCheckpointHash: string;

  // 系统提示
  systemPrompt: string;

  // 工具开关状态
  toolSwitchState?: ToolSwitchState;

  // 时间戳
  lastMessageTs?: number;

  // 停止原因
  stopReason: string;
}

export class StateManager {
  private state: AgentState;
  private _toolSwitchManager: ToolSwitchManager;

  constructor(initialState: Partial<AgentState>) {
    this.state = {
      sessionId: '',
      chatId: '',
      cwd: '',
      didCompleteTask: false,
      startTaskTime: Date.now(),
      startLlmTime: 0,
      abort: false,
      abandoned: false,
      apiConversationHistory: [],
      localMessages: [],
      isWaitingForFirstChunk: false,
      isStreaming: false,
      currentStreamingContentIndex: 0,
      assistantMessageContent: [],
      presentAssistantMessageLocked: false,
      presentAssistantMessageHasPendingUpdates: false,
      userMessageContent: [],
      userMessageContentReady: false,
      didCompleteReadingStream: false,
      didRejectTool: false,
      didAlreadyUseTool: false,
      consecutiveMistakeCount: 0,
      isTooLongForApiReq: false,
      tooLongTip: '',
      didFinishAbortingStream: false,
      modelConfig: {
        model: 'Kwaipilot Pro',
        maxTokens: 30000
      },
      lastCheckpointHash: '',
      systemPrompt: '',
      stopReason: '',
      ...initialState
    };

    // 初始化工具开关管理器
    this._toolSwitchManager = new ToolSwitchManager(initialState.toolSwitchState);
  }

  getState(): Readonly<AgentState> {
    return { ...this.state };
  }

  updateState(updates: Partial<AgentState>): void {
    this.state = { ...this.state, ...updates };
  }

  resetStreamingState(): void {
    this.updateState({
      // 当前正在处理流的索引位置
      currentStreamingContentIndex: 0,
      // 存储 AI 助手的消息内容的数组
      assistantMessageContent: [],
      // 标记是否已完成读取整个流
      didCompleteReadingStream: false,
      // 存储用户消息内容的数组，包含用户输入和反馈的消息
      userMessageContent: [],
      // 标记用户消息内容是否准备就绪
      userMessageContentReady: false,
      // 标记是否拒绝工具
      didRejectTool: false,
      // 标记是否已经使用工具
      didAlreadyUseTool: false,
      // 标记助手消息展示是否被锁定，用于防止消息展示的并发问题
      presentAssistantMessageLocked: false,
      // 标记是否存在待处理的助手消息更新
      presentAssistantMessageHasPendingUpdates: false,
    });
  }

  // 获取特定状态的便捷方法
  get sessionId(): string { return this.state.sessionId; }
  get chatId(): string { return this.state.chatId; }
  get cwd(): string { return this.state.cwd; }
  get didCompleteTask(): boolean { return this.state.didCompleteTask; }
  get abort(): boolean { return this.state.abort; }
  get abandoned(): boolean { return this.state.abandoned; }
  get apiConversationHistory(): (MessageParam | MessageParamVersion0)[] {
    return this.state.apiConversationHistory;
  }
  get localMessages(): LocalMessage[] { return this.state.localMessages; }
  get isStreaming(): boolean { return this.state.isStreaming; }
  get assistantMessageContent(): AssistantMessageContent[] {
    return this.state.assistantMessageContent;
  }
  get userMessageContent(): TextBlockParamVersion1[] { return this.state.userMessageContent; }
  get consecutiveMistakeCount(): number { return this.state.consecutiveMistakeCount; }
  get modelConfig(): { model: string; maxTokens: number } { return this.state.modelConfig; }
  get systemPrompt(): string { return this.state.systemPrompt; }
  get lastCheckpointHash(): string { return this.state.lastCheckpointHash; }
  get startTaskTime(): number { return this.state.startTaskTime; }
  get startLlmTime(): number { return this.state.startLlmTime; }
  get isTooLongForApiReq(): boolean { return this.state.isTooLongForApiReq; }
  get tooLongTip(): string { return this.state.tooLongTip; }
  get didFinishAbortingStream(): boolean { return this.state.didFinishAbortingStream; }
  get stopReason(): string { return this.state.stopReason; }

  // 工具开关管理器相关方法
  get toolSwitchManager(): ToolSwitchManager {
    return this._toolSwitchManager;
  }

  /**
   * 获取启用的工具列表
   */
  getEnabledTools(): string[] {
    return this._toolSwitchManager.getEnabledTools();
  }

  /**
   * 更新工具开关状态
   */
  updateToolSwitchState(toolSwitchState: ToolSwitchState): void {
    this.updateState({ toolSwitchState });
    this._toolSwitchManager = new ToolSwitchManager(toolSwitchState);
  }
} 