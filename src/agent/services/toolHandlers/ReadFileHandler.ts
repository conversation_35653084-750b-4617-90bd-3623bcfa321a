import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import path from 'path';
import { getReadablePath } from '@/util/path';
import { extractTextFromFile } from '../../tools/extract-text';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 读取文件工具处理器
 */
export class ReadFileHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const relPath: string | undefined = block.params.path;
    const sharedMessageProps: SayTool = {
      tool: 'readFile',
      path: getReadablePath(this.context.cwd, ToolHelpers.removeClosingTag('path', relPath, block.partial))
    };
    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: undefined
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!relPath) {
          this.context.stateManager.updateState({ consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1 });
          ToolHelpers.pushToolResult(block, userMessageContent, await ToolHelpers.sayAndCreateMissingParamError('read_file', 'path', this.context), this.context.stateManager);
          return;
        }
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'read_file'
        });
        const absolutePath = path.resolve(this.context.cwd, relPath);
        const startLine: number | undefined = block.params.start_line_one_indexed
          ? parseInt(block.params.start_line_one_indexed)
          : undefined;
        const endLine: number | undefined = block.params.end_line_one_indexed_inclusive
          ? parseInt(block.params.end_line_one_indexed_inclusive)
          : undefined;
        const shouldReadEntireFile: boolean | undefined = block.params.should_read_entire_file === 'true';
        const completeMessage = JSON.stringify({
          ...sharedMessageProps,
          startLine,
          endLine,
          shouldReadEntireFile,
          content: absolutePath
        } satisfies SayTool);

        await this.context.messageService.say('tool', completeMessage, false);
        const toolLog = ToolHelpers.generateToolLog('read_file', this.context.loggerManager);
        toolLog.start(absolutePath);
        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            path: absolutePath
          },
          metadata: {
            name: block.name
          }
        });
        const startToolTime = Date.now();
        const { content, totalLineNum, readedLineNum } = await extractTextFromFile(
          absolutePath,
          shouldReadEntireFile
            ? undefined
            : {
              startLine,
              endLine
            }
        );
        toolLog.end(content);
        generationCall?.end({
          output: { content, totalLineNum }
        });
        ToolHelpers.pushToolResult(block, userMessageContent, content, this.context.stateManager);
        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });
        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          shouldReadEntireFile,
          totalLineNum,
          readedLineNum,
          path: absolutePath,
          startLine,
          endLine
        });
        return;
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'reading file', error, 'read_file');
    }
  }
} 