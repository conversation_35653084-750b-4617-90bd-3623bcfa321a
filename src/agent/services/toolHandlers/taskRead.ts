import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type';
import { LangfuseGenerationClient } from 'langfuse';
import { readFile, writeFile } from 'fs/promises';
import path from 'path';
import { formatResponse } from '../../prompt/responses';
import { ASSISTANT_NAMESPACE } from '@/util/const';

/**
 * 任务状态更新处理器
 */
export class TaskStatusHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) { }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    // 获取任务文件路径和可选的任务名称
    const taskFilePath = block.params.taskFilePath as string;
    const task = block.params.task as string | undefined;

    const sharedMessageProps: SayTool = {
      tool: 'task_read',
      path: taskFilePath
    };

    try {
      if (block.partial) {
        // 处理部分请求
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: 'Processing task status...'
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        // 验证必要参数
        if (!taskFilePath) {
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('task_read', 'taskFilePath', this.context),
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });

        // 获取完整的文件路径
        const fullTaskFilePath = path.isAbsolute(taskFilePath)
          ? taskFilePath
          : path.join(this.context.cwd, taskFilePath);

        const toolLog = ToolHelpers.generateToolLog('taskStatus', this.context.loggerManager);
        toolLog.start(JSON.stringify({ taskFilePath, task }));

        // 执行任务状态更新
        const startToolTime = Date.now();

        try {
          // 读取任务文件
          const fileContent = await readFile(fullTaskFilePath, 'utf8');
          
          // 解析所有任务
          const taskList = this.parseTaskList(fileContent);
          let updatedContent = fileContent;
          let currentTaskCompleted: string | undefined = undefined;

          // 1. 如果有指定任务，将其标记为完成
          if (task) {
            const taskToComplete = taskList.find(t => t.text.includes(task));
            if (taskToComplete) {
              updatedContent = this.updateTaskStatus(updatedContent, taskToComplete.text, '[x]');
              currentTaskCompleted = taskToComplete.text;
              // 更新 taskList 中对应任务的状态
              taskToComplete.status = 'completed';
            }
          }

          // 2. 获取下一个未开始的任务
          const nextTask = taskList.find(t => t.status === 'not_started');
          
          // 3. 如果有下一个任务，将其标记为进行中
          if (nextTask) {
            updatedContent = this.updateTaskStatus(updatedContent, nextTask.text, '[-]');
            nextTask.status = 'in_progress';
          }

          // 写入更新后的内容
          await writeFile(fullTaskFilePath, updatedContent, 'utf8');

          // 构建返回结果
          const result: TaskResult = {
            taskList: this.parseTaskList(updatedContent), // 重新解析更新后的任务列表
          };

          if (currentTaskCompleted) {
            result.currentTaskCompleted = currentTaskCompleted;
          }

          if (nextTask) {
            result.nextTask = nextTask;
          }

          // 返回成功结果
          const successResult = formatResponse.toolSuccess(JSON.stringify(result, null, 2));
          ToolHelpers.pushToolResult(block, userMessageContent, successResult, this.context.stateManager);

          // 记录性能日志
          this.context.loggerManager.perf({
            namespace: ASSISTANT_NAMESPACE,
            subtag: 'kwaipilot-ide-agent-chat-tool',
            millis: Date.now() - startToolTime,
            extra4: 'success',
            extra6: block.name
          });

          // 报告工具使用
          ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
            taskFilePath,
            task,
            totalTasks: taskList.length,
            completedTasks: taskList.filter(t => t.status === 'completed').length
          });

          toolLog.end(JSON.stringify({ success: true, result }));
        } catch (error: any) {
          const errorMessage = `Failed to process task status: ${error.message}`;
          await this.context.messageService.say('tool_error', errorMessage);
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            formatResponse.toolError(errorMessage),
            this.context.stateManager
          );
          toolLog.end(JSON.stringify({ success: false, error: error.message }));
        }
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'process task status', error, 'taskStatus');
    }
  }

  /**
   * 解析任务列表
   * @param content 文件内容
   * @returns 任务列表
   */
  private parseTaskList(content: string): TaskItem[] {
    const lines = content.split('\n');
    const tasks: TaskItem[] = [];

    for (const line of lines) {
      // 匹配任务行格式: - [ ], - [-], - [x]
      const taskMatch = line.match(/^(\s*-\s+\[([ x\-])\]\s+)(.+)$/i);
      if (taskMatch) {
        const [, prefix, statusChar, taskText] = taskMatch;
        let status: 'not_started' | 'in_progress' | 'completed';
        
        switch (statusChar.toLowerCase()) {
          case 'x':
            status = 'completed';
            break;
          case '-':
            status = 'in_progress';
            break;
          default:
            status = 'not_started';
            break;
        }

        tasks.push({
          text: taskText.trim(),
          status,
          originalLine: line
        });
      }
    }

    return tasks;
  }

  /**
   * 更新任务状态标记
   * @param content 文件内容
   * @param taskText 任务文本
   * @param statusMark 状态标记 ([ ], [-], [x])
   * @returns 更新后的内容
   */
  private updateTaskStatus(content: string, taskText: string, statusMark: string): string {
    // 寻找任务文本，并替换任务状态标记
    // 支持不同的格式，如: 
    // - [ ] 任务文本
    // - [-] 任务文本
    // - [x] 任务文本
    // 以及带编号的格式如:
    // - [ ] 1.2 任务文本

    const escapedTask = taskText.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义正则表达式特殊字符

    // 构建正则表达式，匹配任务行并捕获前缀和任务文本
    // 这将匹配任何形式的状态标记 [ ], [-], [x], [X]
    const regex = new RegExp(`^(\\s*-\\s+\\[[ x\\-]\\]\\s+)(${escapedTask})(.*)$`, 'gim');

    // 替换匹配到的行，保持前缀和任务文本不变，仅更新状态标记
    return content.replace(regex, (match, prefix, task, suffix) => {
      // 提取前缀中的缩进和破折号
      const indentDash = prefix.match(/^(\s*-\s+)/)?.[1] || '';
      // 返回新的行，包含原来的缩进、破折号、新的状态标记和原来的任务文本
      return `${indentDash}${statusMark} ${task}${suffix}`;
    });
  }
}
