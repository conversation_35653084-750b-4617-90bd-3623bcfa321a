import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type';
import { LangfuseGenerationClient } from 'langfuse';
import { readFile, writeFile } from 'fs/promises';
import path from 'path';
import { formatResponse } from '../../prompt/responses';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { getImplicitRules } from '../../prompt/research/implicitRules';


/**
 * 任务项接口
 */
interface TaskItem {
  text: string;
  status: 'not_started' | 'in_progress' | 'completed';
  originalLine: string;
}

/**
 * 任务处理结果接口
 */
interface TaskResult {
  taskList: TaskItem[];
  currentTaskCompleted?: string;
  nextTask?: TaskItem;
}

/**
 * 任务状态更新处理器
 */
export class TaskReadHandler implements Too<PERSON><PERSON><PERSON>ler {
  constructor(private context: ToolHandlerContext) { }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    // 获取任务文件路径和可选的任务名称
    const taskFilePath = block.params.taskFilePath as string;
    const completedTask = block.params.completedTask as string | undefined;

    const sharedMessageProps: SayTool = {
      tool: 'readFile',
      path: taskFilePath
    };

    try {
      if (block.partial) {
        // 处理部分请求
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: 'Processing task status...'
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        // 验证必要参数
        if (!taskFilePath) {
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            await ToolHelpers.sayAndCreateMissingParamError('task_read', 'taskFilePath', this.context),
            this.context.stateManager
          );
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });

        // 获取完整的文件路径
        const fullTaskFilePath = path.isAbsolute(taskFilePath)
          ? taskFilePath
          : path.join(this.context.cwd, taskFilePath);

        const toolLog = ToolHelpers.generateToolLog('task_read', this.context.loggerManager);
        toolLog.start(JSON.stringify({ taskFilePath, task }));

        // 执行任务状态更新
        const startToolTime = Date.now();

        try {
          // 读取任务文件
          const fileContent = await readFile(fullTaskFilePath, 'utf8');
          let updatedContent = fileContent;
          let nextTask: TaskItem | undefined = undefined;

          // 1. 如果有指定任务，将其标记为完成
          if (completedTask) {
            // 匹配taskFilePath文件中的任务，更改其状态为complete
            updatedContent = this.updateTaskStatus(updatedContent, completedTask, '[x]');
          }

          // 2. 获取taskFilePath中的全部任务内容
          const taskList = this.parseTaskList(updatedContent);

          // 3. 从上到下获取下一个未开始的任务
          nextTask = taskList.find(t => t.status === 'not_started');

          // 4. 如果有下一个任务，将其在TaskFilePath文件中状态改为进行中
          if (nextTask) {
            updatedContent = this.updateTaskStatus(updatedContent, nextTask.text, '[-]');
          }

          // 写入更新后的内容
          await writeFile(fullTaskFilePath, updatedContent, 'utf8');

          const result = `Implement the task from the markdown document at ${taskFilePath}:
          <task title="${nextTask?.text}">
          Status: ${nextTask?.status}

          Task details:
          
          ## Instructions
Implement the task according to the requirements.
Only focus on ONE task at a time. Do not implement functionality for other tasks.
If a task has sub-tasks, implement the sub-tasks first.
DO NOT end this execution until you have verified ALL sub-tasks, if any, have been completed.
Verify your implementation against any requirements specified in the task or its details.
If you need to execute a command, make sure it is terminable. For example, use the --run flag when running vitest tests

${getImplicitRules('implementation')}
`

          // 返回成功结果
          const successResult = formatResponse.toolSuccess(result);
          ToolHelpers.pushToolResult(block, userMessageContent, successResult, this.context.stateManager);

          // 记录性能日志
          this.context.loggerManager.perf({
            namespace: ASSISTANT_NAMESPACE,
            subtag: 'kwaipilot-ide-agent-chat-tool',
            millis: Date.now() - startToolTime,
            extra4: 'success',
            extra6: block.name
          });

          // 报告工具使用
          ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
            taskFilePath,
            task,
            totalTasks: taskList.length,
            completedTasks: taskList.filter(t => t.status === 'completed').length
          });

          toolLog.end(JSON.stringify({ success: true, result }));
        } catch (error: any) {
          const errorMessage = `Failed to process task status: ${error.message}`;
          await this.context.messageService.say('tool_error', errorMessage);
          ToolHelpers.pushToolResult(
            block,
            userMessageContent,
            formatResponse.toolError(errorMessage),
            this.context.stateManager
          );
          toolLog.end(JSON.stringify({ success: false, error: error.message }));
        }
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'process task status', error, 'task_read');
    }
  }

  /**
   * 解析任务列表
   * @param content 文件内容
   * @returns 任务列表
   */
  private parseTaskList(content: string): TaskItem[] {
    const lines = content.split('\n');
    const tasks: TaskItem[] = [];

    for (const line of lines) {
      // 匹配任务行格式: - [ ], - [-], - [x]
      const taskMatch = line.match(/^(\s*-\s+\[([ x\-])\]\s+)(.+)$/i);
      if (taskMatch) {
        const [, , statusChar, taskText] = taskMatch;
        let status: 'not_started' | 'in_progress' | 'completed';
        
        switch (statusChar.toLowerCase()) {
          case 'x':
            status = 'completed';
            break;
          case '-':
            status = 'in_progress';
            break;
          default:
            status = 'not_started';
            break;
        }

        tasks.push({
          text: taskText.trim(),
          status,
          originalLine: line
        });
      }
    }

    return tasks;
  }

  /**
   * 更新任务状态标记
   * @param content 文件内容
   * @param taskText 任务文本（可以是完整任务文本或部分匹配文本）
   * @param statusMark 状态标记 ([ ], [-], [x])
   * @returns 更新后的内容
   */
  private updateTaskStatus(content: string, taskText: string, statusMark: string): string {
    const lines = content.split('\n');
    let updated = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      // 匹配任务行格式: - [ ], - [-], - [x]
      const taskMatch = line.match(/^(\s*-\s+\[[ x\-]\]\s+)(.+)$/i);

      if (taskMatch) {
        const [, prefix, currentTaskText] = taskMatch;

        // 检查任务文本是否匹配（支持部分匹配）
        if (currentTaskText.includes(taskText) || taskText.includes(currentTaskText.trim())) {
          // 提取前缀中的缩进和破折号
          const indentDash = prefix.match(/^(\s*-\s+)/)?.[1] || '';
          // 更新状态标记，保持原有的任务文本
          lines[i] = `${indentDash}${statusMark} ${currentTaskText}`;
          updated = true;
          break; // 只更新第一个匹配的任务
        }
      }
    }

    if (!updated) {
      console.warn(`Task not found for update: ${taskText}`);
    }

    return lines.join('\n');
  }
}
