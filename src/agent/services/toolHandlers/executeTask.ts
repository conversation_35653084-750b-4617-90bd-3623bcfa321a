import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHandler } from '../ToolHelpers';
import { TextBlockParamVersion1 } from '../../types/type';
import { LangfuseGenerationClient } from 'langfuse';

/**
 * 使用MCP工具处理器
 */
export class ExecuteTaskHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {
  }

  async handle(
    block: ToolUse,
    userMessageContent: TextBlockParamVersion1[]
  ): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const requires_approval: string | undefined = block.params.requires_approval;
    const requires_approval_boolean = requires_approval?.toLowerCase() === 'true';
    const params = {
      tool: 'useMcpTool',
      // serverName: mcpServerName,
      // toolName: mcpToolName,
      // arguments: mcpToolParams,
      requires_approval: requires_approval_boolean
    };
    const messsage = JSON.stringify(params);
    try {
      if (block.partial) {
        await this.context.messageService.ask('use_mcp_tool', messsage, block.partial).catch(() => { });
        return;
      } else {
        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });
        // this.context.loggerManager.reportUserAction({
        //   key: 'agent_tools_request',
        //   type: 'use_mcp_tool',
        // });
        const startAskTime = Date.now();
        const didApprove = await ToolHelpers.askApproval(block, userMessageContent, this.context.messageService, this.context.stateManager, 'use_mcp_tool', messsage);
        // this.context.loggerManager.reportUserAction({
        //   key: 'agent_tools_request_ope',
        //   type: 'use_mcp_tool',
        //   subType: didApprove ? 'tool_run' : 'tool_cancel',
        //   content: JSON.stringify({
        //     mcp_server_name: mcpServerName,
        //     mcp_tool_name: mcpToolName
        //   })
        // });

        // this.context.loggerManager.reportUserAction({
        //   key: 'agent_task',
        //   type: 'tool_use_ask',
        //   content: JSON.stringify({
        //     toolName: block.name,
        //     mcpServerName: mcpServerName,
        //     mcpToolName: mcpToolName,
        //     sessionId: this.context.stateManager.getState().sessionId,
        //     chatId: this.context.stateManager.getState().chatId,
        //     ts: Date.now(),
        //     duration: Date.now() - startAskTime,
        //     didApprove,
        //     requiresApproval: requires_approval
        //   })
        // });
        if (!didApprove) {
          return;
        }
        await this.context.messageService.say('use_mcp_tool_result', '', true);
        const toolLog = ToolHelpers.generateToolLog('use_mcp_tool', this.context.loggerManager);
        // 执行
        const startToolTime = Date.now();

        // ToolHelpers.pushToolResult(block, userMessageContent, result, this.context.stateManager);

        // this.context.loggerManager.perf({
        //   namespace: ASSISTANT_NAMESPACE,
        //   subtag: 'kwaipilot-ide-agent-chat-tool',
        //   millis: Date.now() - startToolTime,
        //   extra4: 'success',
        //   extra6: block.name
        // });
        // this.context.loggerManager.reportUserAction({
        //   key: 'agent_task',
        //   type: 'tool_use',
        //   content: JSON.stringify({
        //     toolName: block.name,
        //     mcpServerName: mcpServerName,
        //     mcpToolName: mcpToolName,
        //     sessionId: this.context.stateManager.getState().sessionId,
        //     chatId: this.context.stateManager.getState().chatId,
        //     ts: Date.now(),
        //     duration: Date.now() - startToolTime,
        //     mcpToolParams,
        //     requires_approval
        //   })
        // });
      }
    } catch (error: any) {
      await ToolHelpers.handleError(block, userMessageContent, this.context, generationCall, 'use mcp tool', error, 'use_mcp_tool');
    }
  }
} 