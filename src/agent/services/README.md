# Agent 架构重构

本目录包含了 `AgentManager` 类的架构重构成果，将原本超过 2400 行的单一类拆分为多个专门的服务类。

## 架构概览

### 核心服务类

1. **StateManager** - 状态管理器
   - 集中管理所有状态变量
   - 提供状态更新和查询接口
   - 支持流式状态重置

2. **MessageService** - 消息和对话服务（合并了原 ConversationManager）
   - 处理 `say` 和 `ask` 消息
   - 管理用户交互响应
   - 管理 API 对话历史
   - 管理本地消息列表
   - 处理环境详情添加
   - 支持部分消息和完整消息

3. **StreamManager** - 流管理器
   - 处理流式响应解析
   - 管理流式状态更新
   - 控制消息展示流程

4. **CheckpointService** - 检查点服务
   - 管理检查点创建和恢复
   - 处理 Git 操作
   - 支持错误恢复

5. **ToolExecutor** - 工具执行器
   - 提供工具描述生成
   - 为未来的工具插件化做准备

### 重构后的 AgentManager

`AgentManager` 类展示了如何使用这些服务类：

```typescript
export class AgentManager {
  private stateManager: StateManager;
  private streamManager: StreamManager;
  private messageService: MessageService;  // 合并了对话管理功能
  private checkpointService: CheckpointService;
  private toolExecutor: ToolExecutor;
  
  // ... 使用依赖注入组合各个服务
}
```

## 架构优势

### 1. 单一职责原则
每个服务类都有明确的职责边界，提高了代码的可维护性。

### 2. 依赖注入
通过构造函数注入依赖，降低了类之间的耦合度。

### 3. 可测试性
每个服务都可以独立进行单元测试。

### 4. 可扩展性
新功能可以通过添加新服务或扩展现有服务来实现。

### 5. 状态管理
统一的状态管理减少了状态同步问题。

## 使用方式

### 创建 Agent 实例

```typescript
const agent = await AgentManager.init(
  messenger,
  cwd,
  sessionInfo,
  restoreInfo
);
```

### 启动任务

```typescript
await agent.startTask();
```

### 停止任务

```typescript
await agent.stop();
```

## 迁移说明

本次重构已经完成了架构优化，将原本超过 2400 行的单一 `AgentManager` 类重构为基于服务分离的架构。主要变化包括：

1. **服务合并**：将 `ConversationManager` 的功能合并到 `MessageService` 中，减少循环依赖
2. **依赖注入**：所有服务都通过构造函数注入依赖
3. **状态集中管理**：通过 `StateManager` 统一管理所有状态
4. **接口兼容**：保持了与原有接口的兼容性

重构后的代码更易于维护、测试和扩展，同时保持了原有的业务逻辑不变。


# 工具开关功能

工具开关功能允许用户动态控制哪些工具可用，从而提供更灵活的工具管理能力。

## 功能特性

### 1. 工具分类管理
- **文件操作**: `read_file`, `edit_file`, `write_to_file`, `replace_in_file`, `list_files`
- **搜索**: `codebase_search`, `grep_search`
- **命令执行**: `execute_command`
- **交互**: `ask_followup_question`
- **外部工具**: `use_mcp_tool`

### 2. 核心工具保护
- 某些工具被标记为"核心工具"（如 `read_file`）
- 核心工具无法被禁用，确保系统的基本功能

### 3. 动态配置
- 支持实时启用/禁用工具
- 支持批量设置工具状态
- 支持状态保存和恢复

## 使用方法

### 1. 创建工具开关管理器

```typescript
import { ToolSwitchManager } from './ToolSwitchManager';

// 使用默认配置创建
const manager = new ToolSwitchManager();

// 从保存的状态恢复
const savedState = {
  switches: {
    'read_file': true,
    'edit_file': true,
    'write_to_file': false,
    // ... 其他工具状态
  },
  lastUpdated: Date.now()
};
const manager = new ToolSwitchManager(savedState);
```

### 2. 工具状态管理

```typescript
// 检查工具是否启用
const isEnabled = manager.isToolEnabled('grep_search');

// 启用工具
manager.enableTool('grep_search');

// 禁用工具
manager.disableTool('grep_search');

// 切换工具状态
manager.toggleTool('grep_search');

// 批量设置工具状态
manager.setToolsState({
  'write_to_file': true,
  'replace_in_file': true,
  'execute_command': false
});
```

### 3. 获取工具信息

```typescript
// 获取所有启用的工具
const enabledTools = manager.getEnabledTools();

// 获取所有禁用的工具
const disabledTools = manager.getDisabledTools();

// 获取工具配置
const toolConfig = manager.getToolConfig('codebase_search');

// 获取所有工具配置
const allConfigs = manager.getAllToolConfigs();

// 按分类获取工具
const fileTools = manager.getToolsByCategory(ToolCategory.FILE_OPERATIONS);
```

### 4. 状态管理

```typescript
// 获取当前状态
const state = manager.getState();

// 重置为默认状态
manager.resetToDefault();

// 获取统计信息
const stats = manager.getStatistics();
console.log(stats);
// 输出: { total: 10, enabled: 8, disabled: 2, coreTools: 2, optionalTools: 8 }
```

## 在系统中的集成

### 1. StateManager 集成

```typescript
// StateManager 已经集成了工具开关管理器
const stateManager = new StateManager(initialState);

// 获取工具开关管理器
const toolSwitchManager = stateManager.toolSwitchManager;

// 获取启用的工具列表
const enabledTools = stateManager.getEnabledTools();

// 更新工具开关状态
stateManager.updateToolSwitchState(newToolSwitchState);
```

### 2. 工具执行检查

```typescript
// ToolExecutor 会自动检查工具是否启用
const toolExecutor = new ToolExecutor(
  stateManager,
  messageService,
  agentTraceLogger,
  trace,
  cwd,
  sessionInfo,
  messenger,
  saveCheckpoint,
  apiConversationHistory,
  lastCheckpointHash,
  toolSwitchManager // 传递工具开关管理器
);

// 执行工具时会自动检查工具是否启用
await toolExecutor.executeToolUse(toolUse, userMessageContent);
```

### 3. 提示词生成

```typescript
// 系统提示词生成时会过滤禁用的工具
const enabledTools = stateManager.getEnabledTools();
const systemPrompt = SYSTEM_PROMPT(
  model,
  cwd,
  mcpServers,
  enableRepoIndex,
  rules,
  shell,
  useNewEditTool,
  enabledTools // 传递启用的工具列表
);
```

## 配置示例

### 1. 基本配置

```typescript
// 只启用文件操作和搜索工具
const basicConfig = {
  switches: {
    'read_file': true,
    'edit_file': true,
    'write_to_file': true,
    'replace_in_file': true,
    'list_files': true,
    'codebase_search': true,
    'grep_search': false,
    'execute_command': false,
    'use_mcp_tool': false,
    'ask_followup_question': true
  },
  lastUpdated: Date.now()
};
```

### 2. 开发环境配置

```typescript
// 开发环境启用所有工具
const devConfig = {
  switches: {
    'read_file': true,
    'edit_file': true,
    'write_to_file': true,
    'replace_in_file': true,
    'list_files': true,
    'codebase_search': true,
    'grep_search': true,
    'execute_command': true,
    'use_mcp_tool': true,
    'ask_followup_question': true
  },
  lastUpdated: Date.now()
};
```

### 3. 受限环境配置

```typescript
// 受限环境只启用基本工具
const restrictedConfig = {
  switches: {
    'read_file': true,
    'edit_file': true,
    'write_to_file': false,
    'replace_in_file': false,
    'list_files': true,
    'codebase_search': true,
    'grep_search': false,
    'execute_command': false,
    'use_mcp_tool': false,
    'ask_followup_question': true
  },
  lastUpdated: Date.now()
};
```

## 最佳实践

### 1. 核心工具保护
- 始终保持核心工具启用，确保系统基本功能
- 核心工具包括 `read_file`

### 2. 分类管理
- 按功能分类管理工具，便于批量操作
- 根据使用场景选择合适的工具组合

### 3. 状态持久化
- 定期保存工具开关状态，便于恢复
- 在系统重启时恢复用户的工具配置

### 4. 权限控制
- 根据用户权限控制工具的可用性
- 在受限环境中禁用危险工具（如 `execute_command`）

## 注意事项

1. **核心工具**: 标记为核心的工具无法被禁用
2. **依赖关系**: 某些工具可能依赖其他工具的功能
3. **性能影响**: 工具开关检查对性能影响很小
4. **向后兼容**: 如果没有指定工具开关，系统会使用默认配置

## 扩展功能

### 1. 工具组合
可以定义常用的工具组合，方便快速切换：

```typescript
const toolPresets = {
  'basic': ['read_file', 'edit_file', 'list_files'],
  'search': ['read_file', 'edit_file', 'codebase_search', 'grep_search'],
  'full': ['read_file', 'edit_file', 'write_to_file', 'replace_in_file', 'list_files', 'codebase_search', 'grep_search', 'execute_command', 'use_mcp_tool', 'ask_followup_question']
};
```

### 2. 条件启用
可以根据条件自动启用或禁用工具：

```typescript
// 例如：只在开发环境启用命令执行
if (process.env.NODE_ENV === 'development') {
  manager.enableTool('execute_command');
} else {
  manager.disableTool('execute_command');
}
```

### 3. 工具使用统计
可以收集工具使用统计，优化工具配置：

```typescript
// 记录工具使用频率
const toolUsageStats = {
  'read_file': 1250,
  'edit_file': 890,
  'codebase_search': 450,
  // ...
};
``` 