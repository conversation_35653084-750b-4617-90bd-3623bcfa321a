/**
 * 工具开关管理器
 */

import { ToolName } from './toolHandlers';
import { ToolSwitchConfig, ToolSwitchState, DEFAULT_TOOL_SWITCHES, ToolCategory } from '../utils/toolSwitches';
import { Logger } from '@/util/log';

/**
 * 工具开关管理器
 */
export class ToolSwitchManager {
  private logger = new Logger('ToolSwitchManager');
  private toolSwitches: Map<ToolName, boolean> = new Map();
  private toolConfigs: Map<ToolName, ToolSwitchConfig> = new Map();

  constructor(initialState?: ToolSwitchState) {
    this.initialize(initialState);
  }

  /**
   * 初始化工具开关
   */
  private initialize(initialState?: ToolSwitchState) {
    // 初始化工具配置
    DEFAULT_TOOL_SWITCHES.forEach(config => {
      this.toolConfigs.set(config.name, config);
    });

    if (initialState) {
      // 从保存的状态恢复，但需要确保核心工具不被禁用
      DEFAULT_TOOL_SWITCHES.forEach(config => {
        const userEnabled = initialState.switches[config.name];
        if (config.isCore) {
          // 核心工具强制启用
          this.toolSwitches.set(config.name, true);
          if (userEnabled === false) {
            this.logger.warn(`核心工具 ${config.name} 不能被禁用，已强制启用`);
          }
        } else {
          // 非核心工具使用用户配置，如果未配置则使用默认值true
          this.toolSwitches.set(config.name, userEnabled !== undefined ? userEnabled : true);
        }
      });
    } else {
      // 使用默认配置
      DEFAULT_TOOL_SWITCHES.forEach(config => {
        this.toolSwitches.set(config.name, config.enabled);
      });
    }

    this.logger.info('工具开关管理器已初始化', {
      enabledTools: this.getEnabledTools(),
      disabledTools: this.getDisabledTools()
    });
  }

  /**
   * 检查工具是否启用
   */
  isToolEnabled(toolName: ToolName): boolean {
    return this.toolSwitches.get(toolName) ?? false;
  }

  /**
   * 启用工具
   */
  enableTool(toolName: ToolName): boolean {
    const config = this.toolConfigs.get(toolName);
    if (!config) {
      this.logger.warn(`工具 ${toolName} 不存在`);
      return false;
    }

    this.toolSwitches.set(toolName, true);
    this.logger.info(`工具 ${toolName} 已启用`);
    return true;
  }

  /**
   * 禁用工具
   */
  disableTool(toolName: ToolName): boolean {
    const config = this.toolConfigs.get(toolName);
    if (!config) {
      this.logger.warn(`工具 ${toolName} 不存在`);
      return false;
    }

    // 检查是否为核心工具
    if (config.isCore) {
      this.logger.warn(`核心工具 ${toolName} 不能被禁用`);
      return false;
    }

    this.toolSwitches.set(toolName, false);
    this.logger.info(`工具 ${toolName} 已禁用`);
    return true;
  }

  /**
   * 切换工具状态
   */
  toggleTool(toolName: ToolName): boolean {
    const currentState = this.isToolEnabled(toolName);
    if (currentState) {
      return this.disableTool(toolName);
    } else {
      return this.enableTool(toolName);
    }
  }

  /**
   * 批量设置工具状态
   */
  setToolsState(switches: Partial<Record<ToolName, boolean>>): void {
    Object.entries(switches).forEach(([toolName, enabled]) => {
      if (enabled !== undefined) {
        if (enabled) {
          this.enableTool(toolName as ToolName);
        } else {
          this.disableTool(toolName as ToolName);
        }
      }
    });
  }

  /**
   * 获取所有启用的工具
   */
  getEnabledTools(): ToolName[] {
    return Array.from(this.toolSwitches.entries())
      .filter(([, enabled]) => enabled)
      .map(([toolName]) => toolName);
  }

  /**
   * 获取所有禁用的工具
   */
  getDisabledTools(): ToolName[] {
    return Array.from(this.toolSwitches.entries())
      .filter(([, enabled]) => !enabled)
      .map(([toolName]) => toolName);
  }

  /**
   * 获取工具配置
   */
  getToolConfig(toolName: ToolName): ToolSwitchConfig | undefined {
    return this.toolConfigs.get(toolName);
  }

  /**
   * 获取所有工具配置
   */
  getAllToolConfigs(): ToolSwitchConfig[] {
    return Array.from(this.toolConfigs.values());
  }

  /**
   * 按分类获取工具配置
   */
  getToolsByCategory(category: ToolCategory): ToolSwitchConfig[] {
    return Array.from(this.toolConfigs.values())
      .filter(config => config.category === category);
  }

  /**
   * 获取当前状态
   */
  getState(): ToolSwitchState {
    const switches = {} as Partial<Record<ToolName, boolean>>;
    this.toolSwitches.forEach((enabled, toolName) => {
      switches[toolName] = enabled;
    });

    return {
      switches,
      lastUpdated: Date.now()
    };
  }

  /**
   * 重置为默认状态
   */
  resetToDefault(): void {
    this.toolSwitches.clear();
    DEFAULT_TOOL_SWITCHES.forEach(config => {
      this.toolSwitches.set(config.name, config.enabled);
    });
    this.logger.info('工具开关已重置为默认状态');
  }

  /**
   * 获取工具开关统计信息
   */
  getStatistics(): {
    total: number;
    enabled: number;
    disabled: number;
    coreTools: number;
    optionalTools: number;
  } {
    const total = this.toolConfigs.size;
    const enabled = this.getEnabledTools().length;
    const disabled = this.getDisabledTools().length;
    const coreTools = Array.from(this.toolConfigs.values())
      .filter(config => config.isCore).length;
    const optionalTools = total - coreTools;

    return {
      total,
      enabled,
      disabled,
      coreTools,
      optionalTools
    };
  }
} 