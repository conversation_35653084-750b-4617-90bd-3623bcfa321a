import CheckpointTracker from '../tools/checkpoints/CheckpointTracker';
import { StateManager } from './StateManager';
import { MessageService } from './MessageService';
import pTimeout from 'p-timeout';
import { LangfuseTraceClient, LangfuseGenerationClient } from 'langfuse';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { LoggerManager } from './LoggerManager';
import { getTrace } from '@/util/langfuse';
import { WebviewMessage } from '../types/type.d';
import { GlobalConfig } from '@/util/global';

export class CheckpointService {
  private checkpointTracker?: CheckpointTracker;
  private checkpointTrackerErrorMessage?: string;
  private trace: LangfuseTraceClient | null = null;

  constructor(
    private stateManager: StateManager,
    private messageService: MessageService,
    private loggerManager: LoggerManager
  ) { }

  async initCheckpointTracker(type: 'newTask' | 'restore' = 'newTask'): Promise<void> {
    const state = this.stateManager.getState();
    const startTime = Date.now();
    if (!this.checkpointTracker && !this.checkpointTrackerErrorMessage) {
      const traceCheckpointInit = this.trace?.generation({
        name: `checkpoint_init_${type}`,
        input: {
          sessionId: state.sessionId,
          cwd: state.cwd
        }
      });
      try {
        this.checkpointTracker = await pTimeout(CheckpointTracker.create(state.sessionId, state.cwd), {
          milliseconds: 15_000,
          message:
            'Checkpoints taking too long to initialize. Consider re-opening Kwaipilot in a project that uses git, or disabling checkpoints.'
        });
        traceCheckpointInit?.end({
          output: { init: 'success' }
        });
        this.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-initCheckpoint',
          millis: Date.now() - startTime,
          extra4: `success`
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error('Failed to initialize checkpoint tracker:', errorMessage);
        traceCheckpointInit?.end({
          output: { error: 'Failed to initialize checkpoint tracker: ' + errorMessage }
        });
        this.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-initCheckpoint',
          millis: Date.now() - startTime,
          extra4: `error`
        });
        this.checkpointTrackerErrorMessage = errorMessage; // will be displayed right away since we saveClineMessages next which posts state to webview
      }
    }
  }

  async saveCheckpoint(): Promise<void> {
    const state = this.stateManager.getState();
    const startTime = Date.now();
    const traceCheckpointCreate = this.trace?.generation({
      name: 'checkpoint_created'
    });
    if (!this.checkpointTracker) {
      // await this.say('error', '检查点初始化失败，无法创建检查点');
      return;
    }
    try {
      const commitHash = await this.checkpointTracker?.commit();
      if (!commitHash) return;
      this.stateManager.updateState({ lastCheckpointHash: commitHash });
      await this.messageService.say('checkpoint_created', commitHash);
      traceCheckpointCreate?.end({
        output: { commitHash }
      });
      this.loggerManager.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-saveCheckpoint',
        millis: Date.now() - startTime,
        extra4: `success`
      });
    } catch (error) {
      // await this.say('error', '检查点创建失败，无法创建检查点');
      traceCheckpointCreate?.end({
        output: { error: 'Failed to create checkpoint: ' + error }
      });
      this.loggerManager.perf({
        namespace: ASSISTANT_NAMESPACE,
        subtag: 'kwaipilot-ide-agent-saveCheckpoint',
        millis: Date.now() - startTime,
        extra4: `error`
      });
    }
  }

  async restoreCheckpoint(commitHash: string): Promise<void> {
    const state = this.stateManager.getState();
    this.loggerManager.reportUserAction({
      key: 'agent_task_restore',
      type: 'agent_task_restore_start',
      content: JSON.stringify({
        sessionId: state.sessionId
      })
    });
    const username = GlobalConfig.getConfig().getUsername();
    this.trace = getTrace(state.sessionId, username, {
      sessionId: state.sessionId,
      metadata: {
        action: 'restore'
      },
      input: { commitHash }
    });
    // 先初始化检查点跟踪器
    await this.initCheckpointTracker('restore');

    const traceCheckpointReset = this.trace?.generation({
      name: 'checkpoint_restore',
      input: {
        sessionId: state.sessionId,
        cwd: state.cwd,
        commitHash
      }
    });
    if (commitHash && this.checkpointTracker) {
      try {
        await this.checkpointTracker.resetHead(commitHash);
        traceCheckpointReset?.end({
          output: { status: 'success' }
        });
        this.loggerManager.reportUserAction({
          key: 'agent_task_restore',
          type: 'agent_task_restore_success',
          content: JSON.stringify({
            sessionId: state.sessionId,
            modelName: state.modelConfig.model
          })
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        await this.messageService.addToWebviewMessages({
          ts: Date.now(),
          type: 'say',
          say: 'error',
          text: errorMessage,
          sessionId: state.sessionId,
          chatId: state.chatId
        });
        traceCheckpointReset?.end({
          output: { status: 'failed', error: errorMessage }
        });
        this.loggerManager.reportUserAction({
          key: 'agent_task_restore',
          type: 'agent_task_restore_failed',
          content: JSON.stringify({
            sessionId: state.sessionId,
            modelName: state.modelConfig.model,
            error: errorMessage
          })
        });
      }
    }
  }

  getLastCheckpointHash(): string {
    return this.stateManager.getState().lastCheckpointHash;
  }

  hasCheckpointTracker(): boolean {
    return !!this.checkpointTracker;
  }

  getCheckpointErrorMessage(): string | undefined {
    return this.checkpointTrackerErrorMessage;
  }

  setCheckpointErrorMessage(errorMessage: string | undefined): void {
    this.checkpointTrackerErrorMessage = errorMessage;
  }

  setTrace(trace: LangfuseTraceClient | null): void {
    this.trace = trace;
  }

} 