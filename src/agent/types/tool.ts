export type EditDataList = {
    filePath: string;
    replace: string;
    search: string;
}[]

export type ReportGenerateCodeRequest = {
  chatId: string;
  language: string;
  modelType: string;
  platform: string;
  sessionId: string;
  editDataList: EditDataList;
};

export type ReportCodeGenerate = (data: EditDataList) => Promise<void>;

export enum TaskStatus {
  Completed = 1,
  InProgress = 2,
  NotStarted = 3,
}