import { Api } from '@/http';
import { GlobalConfig } from '@/util/global';

/**
 * 从API获取模型配置
 * @returns 返回从API获取的模型配置
 */
export async function fetchModelConfig(data: {
  modelId?: string;
  username: string;
  preference?: string;
}): Promise<{ model: string; maxTokens: number }> {
  const httpClient = new Api();
  // 调用API获取模型配置
  const response = await httpClient.get<{ name: string; maxInputTokens: number }>(
    `/eapi/kwaipilot/plugin/model-config?username=${data.username}${data.preference ? `&mode=${data.preference}` : ''}${data.modelId ? `&model=${data.modelId}` : ''
    }`,
    {},
    {
      signal: AbortSignal.timeout(3000)
    }
  );

  return { model: response.data.name, maxTokens: response.data.maxInputTokens };
}

/**
 * 从kconf获取白名单配置
 * @returns 返回从API获取的模型配置
 */
export async function fetchGrayConfig(data: { grayKey: string; username: string }): Promise<boolean> {
  const { grayKey, username } = data;
  const httpClient = new Api();
  // 调用API获取模型配置
  const response = await httpClient.get<{ full: boolean; name: string; whitelist: string[] }[]>(
    `eapi/kwaipilot/plugin/v2/config?key=kwaipilot.platform.user_gray_config`
  );

  // 是否包含当前用户
  const targetConfig = response.data.find((item) => item.name === grayKey);
  if (!targetConfig) {
    return false;
  }
  if (targetConfig.full) {
    return true;
  }
  if (targetConfig.whitelist.includes(username)) {
    return true;
  }
  return false;
}

export async function checkEnableRepoIndex(): Promise<boolean> {
  try {
    const httpClient = new Api();
    let repoUrl = GlobalConfig.getConfig().getRepoInfo()?.git_url;
    if (!repoUrl) {
      repoUrl = GlobalConfig.getConfig().getRepoPath();
    }
    const data = await httpClient.getRepoIndexFileCount(repoUrl);
    return data?.count > 0;
  } catch (e) {
    return true;
  }
}