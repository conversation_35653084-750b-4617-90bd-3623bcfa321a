// import { TaskStatus } from '../types/tool';

// function serializeTask(task2) {
//   let contextInfo = "";
//   const parts2 = [];
//   parts2.push(`<task title="${task2.text}">`);
//   let statusText;
//   switch (task2.markdownStatus) {
//     case TaskStatus.Completed:
//       statusText = "done";
//       break;
//     case TaskStatus.InProgress:
//       statusText = "in progress";
//       break;
//     case TaskStatus.NotStarted:
//     default:
//       statusText = "not started";
//       break;
//   }
//   parts2.push(`Status: ${statusText}`);
//   if (task2.content) {
//     parts2.push(`Task details:
// ${task2.content}`);
//   }
//   if (task2.subTasks.length > 0) {
//     const subTaskContent = task2.subTasks.map(serializeTask);
//     parts2.push(`Subtasks:

// ${subTaskContent.join("\n\n")}`);
//   }
//   parts2.push("</task>");
//   if (parts2.length > 0) {
//     contextInfo = parts2.join("\n\n");
//   }
//   return contextInfo;
// }