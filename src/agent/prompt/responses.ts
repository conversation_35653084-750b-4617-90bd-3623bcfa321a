import * as path from 'path';
import { TextBlockParam } from '../types/type';

export const formatResponse = {
  toolDenied: () => `The user denied this operation.`,

  toolError: (error?: string) => `The tool execution failed with the following error:\n<error>\n${error}\n</error>`,

  toolSuccess: (message: string) => `Operation completed successfully: ${message}`,

  kwaipilotIgnoreError: (path: string) =>
    `Access to ${path} is blocked by the .kwaipilotignore file settings. You must try to continue in the task without using this file, or ask the user to update the .kwaipilotignore file.`,

  tooManyMistakes: (feedback?: string) =>
    `You seem to be having trouble proceeding. The user has provided the following feedback to help guide you:\n<feedback>\n${feedback}\n</feedback>`,

  missingToolParameterError: (paramName: string) =>
    `Missing value for required parameter '${paramName}'. Please retry with complete response.\n\n${toolUseInstructionsReminder}`,

  invalidMcpToolArgumentError: (serverName: string, toolName: string) =>
    `Invalid JSON argument used with ${serverName} for ${toolName}. Please retry with a properly formatted JSON argument.`,

  toolResult: (text: string): string | Array<TextBlockParam> => {
    return text;
  },

  formatFilesList: (absolutePath: string, files: string[], didHitLimit: boolean): string => {
    const sorted = files
      .map((file) => {
        const relativePath = path.relative(absolutePath, file).toPosix();
        return file.endsWith('/') ? relativePath + '/' : relativePath;
      })
      .sort((a, b) => {
        const aParts = a.split('/'); // only works if we use toPosix first
        const bParts = b.split('/');
        for (let i = 0; i < Math.min(aParts.length, bParts.length); i++) {
          if (aParts[i] !== bParts[i]) {
            // If one is a directory and the other isn't at this level, sort the directory first
            if (i + 1 === aParts.length && i + 1 < bParts.length) {
              return -1;
            }
            if (i + 1 === bParts.length && i + 1 < aParts.length) {
              return 1;
            }
            // Otherwise, sort alphabetically
            return aParts[i].localeCompare(bParts[i], undefined, {
              numeric: true,
              sensitivity: 'base'
            });
          }
        }
        // If all parts are the same up to the length of the shorter path,
        // the shorter one comes first
        return aParts.length - bParts.length;
      });

    if (didHitLimit) {
      return `${sorted.join(
        '\n'
      )}\n\n(File list truncated. Use list_files on specific subdirectories if you need to explore further.)`;
    } else if (sorted.length === 0 || (sorted.length === 1 && sorted[0] === '')) {
      return 'No files found.';
    } else {
      return JSON.stringify(sorted);
      // return sorted.join("\n")
    }
  }
};

const toolUseInstructionsReminder = `# Reminder: Instructions for Tool Use

Tool uses are formatted using XML-style tags. The tool name is enclosed in opening and closing tags, and each parameter is similarly enclosed within its own set of tags. Here's the structure:

<tool_name>
<parameter1_name>value1</parameter1_name>
<parameter2_name>value2</parameter2_name>
...
</tool_name>

For example:

<ask_followup_question>
<question>Your question here</question>
</ask_followup_question>

Always adhere to this format for all tool uses to ensure proper parsing and execution.`;
