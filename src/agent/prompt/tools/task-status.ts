import { ToolArgs } from "./types"

export function getTaskStatusDescription(args: ToolArgs): string {
	return `## task_read
Description: This tool return last completed task and get next task.

Tool instructions:
- Use this tool when
	- You are starting execute task from a spec/plan
	- You have completed a task and want to move on to the next task from the spec/plan
- this tool will return the next task and the whole task list
- When executing a task with sub-tasks, always start with the sub tasks.
Parameters:
- taskFilePath: (required) The path to the \`tasks.md\` file for the spec/plan you are executing. (relative to the current workspace directory ${args.cwd})
- lastTask: (optional) The task that you have completed. It must match the text in the task file exactly. For example if the task file has the text \`  - [-] 3.2 This is my task\`, you should call this tool with \`3.2 This is my task\`.
Usage:
<task_read>
<taskFilePath>.kwaipilot/specs/node-layout-controls/tasks.md</taskFilePath>
<task>1. 设置项目结构和核心接口</task>
</task_read>
`;
}
