const implementationRule = `You are working on the implementation plan. Ask the user to review the plan and confirm if it covers all necessary tasks. 
Ensure each task is actionable, references specific requirements, and focuses only on coding activities. 
Once approved, inform the user that the spec is complete and they can begin implementing the tasks by opening the tasks.md file.`;

const planRule = `Focus on creating a new spec file or identifying an existing spec to update. 
If starting a new spec, create a requirements.md file in the .kwaipilot/specs directory with clear user stories and acceptance criteria. 
If working with an existing spec, review the current requirements and suggest improvements if needed. 
Do not make direct code changes yet. First establish or review the spec file that will guide our implementation.`;

export const getImplicitRules = (type: 'implementation' | 'plan') => `<implicit-rules>## Implicit Rules
${type === 'implementation' ? implementationRule : planRule}
</implicit-rules>`;