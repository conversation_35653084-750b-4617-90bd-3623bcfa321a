import { TaskTable, RepoStatusTable } from '@/db/sqlite/tables/index';
import { clearIndex } from '@/indexing';
import { IdePlatform, IndexAction } from '@/protocol/ideCore';
import { nanoid } from 'nanoid';
import { IMessenger } from '@/protocol/messenger';
import type { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { Logger } from '@/util/log';
import { generateCollectionName, getCacheCollectionName, MAX_INDEX_FILE_COUNT } from '@/util/const';
import path from 'path';
import { FileSystemHelper } from './FileSystemHelper';
import { GitManager } from './GitManager';
import { IndexOptions, IndexTaskAction, RepoIndexOptions } from './types';
import fs from 'fs';

import { getLanceDbPath, getSqlitePath } from '@/util/paths';
import { RepoStatusEnum } from '@/db/sqlite/tables/repo-status';
import { RepoStatusMessageMap } from '@/db/sqlite/tables/repo-status';
import ConcurrencyLock from './ConcurrencyLock';
import { isTextFile } from './utils';
import { Api } from '@/http';
import { convertToGitSshFormat } from '@/util/git-utils';

import fsPromises from 'fs/promises';
import { SqliteDb } from '@/db/sqlite';
import { GlobalConfig } from '@/util/global';
import { IndexUserFilesResponse } from '@/http/types';
import { generateDirectoryStructureText } from '@/embedding/CodeEmbedding';
const AGENT_NAMESPACE = 'agent-state-cloud';

export enum IndexTaskStatus {
  PENDING = 'pending',
  INDEXING = 'indexing'
}
const INDEX_MANAGER_NAMESPACE = 'index_manager_cloud';
export interface IndexTask {
  id: string;
  filepath: string;
  repoDir: string;
  status: IndexTaskStatus;
  fileAction: IndexTaskAction;
  createdAt: number;
  updatedAt: number;
}
export class CloudIndexManager {
  private readonly logger: Logger = new Logger('IndexManager');
  private readonly fileSystem: FileSystemHelper;
  private readonly gitManager: GitManager;
  private autoExecuteTimer: NodeJS.Timeout | null = null;
  // 用户暂停构建时使用这个字段判断当前状态
  private status: 'idle' | 'executing' | 'paused' = 'idle';
  // 定时任务是否正在执行，防止本次未执行完，下次已经开始。
  private isExecuting: boolean = false;
  // 是否取消当前任务,构建过程中，如果用户手动停止，则设置为 true
  private shouldCancel: boolean = false;
  private _dirPath: string = '';
  // 是否是 repoindex，如果是则发送通知消息
  private isRepoBuilding = false;
  // 这个地方不需要的了，容易会出现已经处理过的文件，更改之后，不会被索引。
  // private recentProcessedFiles: string[] = [];
  private timer: number = 5 * 60 * 1000;
  private httpClient = new Api();
  private starttime = Date.now();
  constructor(
    private readonly messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    private readonly options: IndexOptions = {
      maxFileSize: 1 * 1024 * 1024, // 1M
      batchSize: 30,
      delayPerFile: 100,
      delayPerBatch: 1000,
      maxCacheSize: 10000,
      cacheTTL: 24 * 60 * 60 * 1000
    }
  ) {
    this.fileSystem = new FileSystemHelper();
    this.gitManager = new GitManager();
  }

  get dirPath() {
    if (!this._dirPath) {
      this.logger.error('dirPath is not defined');
      return '';
    }
    return this._dirPath;
  }
  set dirPath(dirPath: string) {
    if (!dirPath) {
      this.logger.error('dirPath is required');
      return;
    }
    if (dirPath === this._dirPath) return;
    this.logger.error(`workspace dirPath changed, stop current task, ${this._dirPath} -> ${dirPath}`);
    // 停止当前正在执行的任务
    this.shouldCancel = true;
    this.status = 'paused';
    // 等待当前任务完成
    const waitForTaskToFinish = async () => {
      while (this.isExecuting) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    };

    // 清理当前状态
    const cleanup = async () => {
      // 等待任务完成
      await waitForTaskToFinish();

      // 清理定时器
      if (this.autoExecuteTimer) {
        clearTimeout(this.autoExecuteTimer);
        this.autoExecuteTimer = null;
      }

      // 重置所有状态
      this.shouldCancel = false;
      this.isExecuting = false;
      this.status = 'idle';
      this.isRepoBuilding = false;
    };

    cleanup().then(() => {
      this._dirPath = dirPath;
      // this.init();
    });
  }

  /**
   * 用户手动停止构建
   */
  async pauseExecute() {
    try {
      this.shouldCancel = true;
      if (this.status === 'paused') {
        this.logger.info('pauseExecute: already paused');
        return false;
      }
      this.status = 'paused';
      if (this.autoExecuteTimer) {
        clearTimeout(this.autoExecuteTimer);
        this.autoExecuteTimer = null;
      }

      return true;
    } catch (error) {
      this.logger.error('Error in pauseExecute:', error);
      return false;
    }
  }

  /**
   * 用户手动执行构建
   */
  async startExecute(): Promise<boolean> {
    let logCPUInfoInterval: NodeJS.Timeout | null = null;
    try {
      if (this.status === 'executing') {
        this.logger.info('startExecute: already executing');
        return false;
      }
      this.status = 'executing';

      logCPUInfoInterval = setInterval(() => {
        this.logger.logCPUInfo(INDEX_MANAGER_NAMESPACE, 'startExecute');
      }, 1000);
      this.executeIndexTasks(this.dirPath).then(() => {
        this.logger.info('startExecute: finished----------------' + (Date.now() - this.starttime));
        if (logCPUInfoInterval) {
          clearInterval(logCPUInfoInterval);
        }
        this.status = 'idle';
        this.scheduleNextExecution();
      });
      return true;
    } catch (error) {
      if (logCPUInfoInterval) {
        clearInterval(logCPUInfoInterval);
      }
      this.logger.error('Error in startExecute:', error);
      return false;
    }
  }

  private async checkIfNeedAddTask(filePath: string, dirPath: string): Promise<boolean> {
    try {
      if (filePath.toLowerCase().indexOf('readme') > -1) {
        return true;
      }

      // 检查是否为文本文件
      if (!(await isTextFile(filePath, dirPath))) {
        return false;
      }
      // 判断文件大小，是否超过 1M
      const fullPath = path.join(dirPath, filePath);
      const fileSize = await this.fileSystem.getFileSize(fullPath);
      if (fileSize > this.options.maxFileSize) {
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error checking if file needs to be added to index: ${filePath}`, error);
      return false;
    }
  }
  /**
   * 真正添加文件到 【待索引区】
   */
  private async addTask(filePath: string, dirPath: string, action: IndexTaskAction, gitUrl: string): Promise<void> {
    try {
      if (!(await this.checkIfNeedAddTask(filePath, dirPath))) {
        return;
      }

      this.logger.info(`Adding task for file: ${filePath}`);
      const taskId = nanoid();
      await TaskTable.create({
        filepath: filePath,
        dir_path: dirPath,
        file_action: action,
        created_at: Date.now(),
        updated_at: Date.now(),
        task_id: taskId,
        git_url: gitUrl,
        status: 'pending',
        retry_count: 0
      });
    } catch (error) {
      this.logger.error(`Error adding task for file ${filePath}:`, error);
      // 不抛出错误，继续处理其他文件
    }
  }

  /**
   * 定时执行
   */
  private async scheduleNextExecution() {
    this.updateRepoInfo();
    await this.updateUserChangedFile();
    this.logger.info('scheduleNextExecution start');
    if (this.status === 'paused') {
      this.logger.info('Index execution is paused, skipping schedule');
      return;
    }

    // 清理之前的定时器
    if (this.autoExecuteTimer) {
      this.logger.info('Clearing previous timer');
      clearTimeout(this.autoExecuteTimer);
      this.autoExecuteTimer = null;
    }
    // 如果当前有任务在执行，等待其完成
    if (this.isExecuting) {
      this.logger.info('Task is currently executing, waiting for completion');
      this.shouldCancel = true;
      while (this.isExecuting) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
      this.shouldCancel = false;
    }

    // 检查是否有待处理的任务
    const pendingTasks = await TaskTable.countTasks(this.dirPath, ['pending', 'indexing']);
    this.logger.info(`Found ${pendingTasks} pending/indexing tasks`);
    if (pendingTasks === 0) {
      this.logger.info('No pending tasks, scheduling next check');
      this.autoExecuteTimer = setTimeout(() => this.scheduleNextExecution(), this.timer);
      return;
    }
    const logCPUInfoInterval = setInterval(() => {
      this.logger.logCPUInfo(INDEX_MANAGER_NAMESPACE, 'scheduleNextExecution');
    }, 1000);
    try {
      this.status = 'executing';
      this.logger.info(`Starting to execute ${pendingTasks} tasks`);
      await this.executeIndexTasks(this.dirPath);
      this.logger.info('Task execution completed successfully');
    } catch (error) {
      this.logger.error('Error during task execution:', error);
    } finally {
      if (logCPUInfoInterval) {
        clearInterval(logCPUInfoInterval);
      }
      if (this.status === 'executing') {
        // 只有在执行状态下才改变状态和调度下一次执行
        this.status = 'idle';
        this.logger.info('Scheduling next execution');
        this.autoExecuteTimer = setTimeout(() => this.scheduleNextExecution(), this.timer);
      } else {
        this.logger.info(`Not scheduling next execution, current status: ${this.status}`);
      }
    }
  }

  /**
   * 更新进度并通知前端
   */
  private async updateProgress(
    dirPath: string,
    totalFiles: number,
    doneFiles: number,
    isBuilding: boolean,
    currentFile: string = '',
    action: IndexAction = IndexAction.INDEX_FILE
  ) {
    try {
      await RepoStatusTable.updateProgress(dirPath, totalFiles, doneFiles, isBuilding);
      this.sendProgress(
        totalFiles > 0 ? Math.max(doneFiles / totalFiles, 0.01) : 1, // 0.01 表示最小进度
        totalFiles,
        doneFiles,
        currentFile,
        action,
        currentFile ? `索引中: ${currentFile}` : ''
      );
    } catch (error) {
      this.logger.error('Error in updateProgress:', error);
    }
  }
  private async sendProgress(
    progress: number,
    totalFiles: number,
    doneFiles: number,
    currentFile: string = '',
    action: IndexAction = IndexAction.INDEX_FILE, // 加一个 error 的 action
    message: string = ''
  ) {
    try {
      // 发送进度到前端
      this.messenger.send('index/progress', {
        progress,
        total: totalFiles,
        done: doneFiles,
        filepath: currentFile,
        action: action,
        message: message
      });
    } catch (error) {
      this.logger.error('Error in sendProgress:', error);
    }
  }

  /**
   * 优化后的执行索引任务方法
   */
  private async executeIndexTasks(dirPath: string) {
    // 执行之前首先判断是否被其他实例锁定
    try {
      await ConcurrencyLock.acquireLock(this.dirPath);
    } catch (error) {
      this.logger.error('Error acquiring lock:', error);
      return;
    }
    const startExecuteTime = Date.now();

    this.logger.info('executeIndexTasks: Starting execution');
    if (this.status !== 'executing') {
      this.logger.info('Index execution is not in executing state');
      return;
    }

    this.isExecuting = true;
    let totalCount = 0;

    try {
      this.logger.info('Cleaning up caches');

      const BATCH_SIZE = 1000;
      let lastProcessedId = 0; // 用于追踪最后处理的任务ID
      let totalProcessed = 0;
      let hasMoreTasks = true;

      totalCount = await TaskTable.countTasks(dirPath, ['pending', 'indexing']);
      this.logger.info(`Total tasks to process: ${totalCount}`);

      if (totalCount === 0) {
        this.logger.info('No pending or indexing tasks');
        return;
      }
      if (!this.isRepoBuilding && totalCount > 500) {
        this.isRepoBuilding = true;
        await this.updateProgress(dirPath, totalCount, 1, true, '索引开始构建···');
        this.logger.info('Total tasks to process is greater than 500, set isRepoBuilding to true');
      }

      while (hasMoreTasks && this.status === 'executing' && !this.shouldCancel) {
        this.logger.debug(`Fetching next batch of tasks after ID ${lastProcessedId}`);
        // 修改为基于ID的分页查询
        const tasks = await TaskTable.findTasksAfterIdWithLimit(
          dirPath,
          ['pending', 'indexing'],
          lastProcessedId,
          BATCH_SIZE
        );

        this.logger.info(`Retrieved ${tasks.length} tasks in current batch`);

        if (tasks.length === 0) {
          this.logger.info('No more tasks to process');
          hasMoreTasks = false;
          break;
        }

        // 更新最后处理的ID
        lastProcessedId = tasks[tasks.length - 1]?.id ?? 0;
        this.logger.debug(`Last processed ID updated to ${lastProcessedId}`);

        const tasksToIndex: typeof tasks = [];
        const taskIdsToUpdate: string[] = [];
        // 首先筛选出需要索引的任务和需要跳过的任务
        for (const task of tasks) {
          // 检查是否需要取消
          if (this.shouldCancel || this.status !== 'executing') {
            this.logger.info('Task execution cancelled');
            return;
          }

          // 24 小时内重试 3 次
          if (task.retry_count && task.retry_count >= 3 && Date.now() - task.updated_at < 1000 * 60 * 60 * 24) {
            this.logger.info(`Task ${task.filepath} retry count exceeds 3, skipping`);
            continue;
          }

          // 超过 24 小时则重置重试次数
          if (Date.now() - task.updated_at > 1000 * 60 * 60 * 24) {
            task.retry_count = 0;
          }

          taskIdsToUpdate.push(task.task_id);
          tasksToIndex.push(task);
        }
        // 批量更新成功的任务状态为 indexing
        if (taskIdsToUpdate.length > 0) {
          await TaskTable.batchUpdateStatusByTaskIds(dirPath, taskIdsToUpdate, 'indexing');
        }
        // 使用队列方式批量处理需要索引的任务
        if (tasksToIndex.length > 0) {
          if (this.shouldCancel || this.status !== 'executing') {
            this.logger.info('Task execution cancelled during batch processing');
            return;
          }

          try {
            // 使用队列方式处理文件索引
            await this.processTasksWithQueues(tasksToIndex, dirPath, totalCount, totalProcessed);
            totalProcessed += tasksToIndex.length;
            if (this.isRepoBuilding) {
              await this.updateProgress(
                dirPath,
                totalCount,
                totalProcessed,
                true,
                tasksToIndex[tasksToIndex.length - 1].filepath
              );
            }

            this.logger.info(`Processed ${tasksToIndex.length} tasks with queue-based concurrent processing`);
          } catch (error) {
            // 批量索引失败，将所有任务标记为失败
            const errorDetails = {
              message: error instanceof Error ? error.message : String(error),
              stack: error instanceof Error ? error.stack : undefined,
              code: (error as any)?.code,
              type: error?.constructor?.name,
              dirPath: this.dirPath,
              status: this.status,
              isExecuting: this.isExecuting,
              totalCount,
              batchSize: tasksToIndex.length
            };

            this.logger.error('Error executing batch index tasks:', {
              error: errorDetails,
              context: 'executeIndexTasks',
              timestamp: new Date().toISOString()
            });
            // Report error metrics
            this.logger.perf({
              namespace: INDEX_MANAGER_NAMESPACE,
              subtag: 'execute_batch_index_tasks_error',
              millis: Date.now(),
              extra3: errorDetails.type,
              extra4: errorDetails.message,
              extra5: this.dirPath,
              extra6: JSON.stringify(errorDetails)
            });

            // 将批次中的所有任务标记为失败并增加重试次数
            for (const task of tasksToIndex) {
              try {
                await TaskTable.updateStatusByTaskId(task.dir_path, task.task_id, 'pending', task.retry_count + 1 || 1);
                totalProcessed++;

                if (this.isRepoBuilding) {
                  await this.updateProgress(dirPath, totalCount, totalProcessed, true, task.filepath);
                }
              } catch (updateError) {
                this.logger.error(`Error updating failed task ${task.filepath}:`, updateError);
              }
            }
          }
        }
        this.logger.reportUserAction({
          key: 'kwapilot-binary-' + INDEX_MANAGER_NAMESPACE,
          subType: 'executing_index_tasks_per_batch',
          content: JSON.stringify({
            dirPath,
            time: Date.now() - startExecuteTime,
            fileCount: totalCount,
            processedCount: totalProcessed,
            isRepoBuilding: this.isRepoBuilding
          })
        });
        if (!this.shouldCancel) {
          this.logger.debug('Batch completed, triggering garbage collection');
          global.gc?.();
        }
      }
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'executing_index_tasks_cost',
        millis: Date.now() - startExecuteTime,
        extra3: dirPath,
        extra4: totalCount.toString(),
        extra6: this.isRepoBuilding ? 'true' : 'false'
      });
      this.logger.reportUserAction({
        key: 'kwapilot-binary-' + INDEX_MANAGER_NAMESPACE,
        subType: 'executing_index_tasks',
        content: JSON.stringify({
          dirPath,
          time: Date.now() - startExecuteTime,
          fileCount: totalCount,
          processedCount: totalProcessed,
          isRepoBuilding: this.isRepoBuilding
        })
      });
      this.logger.info('Optimizing database table');
      this.logger.info('startExecute: finished----------------' + (Date.now() - this.starttime));

      this.logger.info('Database table optimization completed');
    } catch (error) {
      const errorDetails = {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        code: (error as any)?.code,
        type: error?.constructor?.name,
        dirPath: this.dirPath,
        status: this.status,
        isExecuting: this.isExecuting,
        totalCount
      };

      this.logger.error('Error executing index tasks---2222:', {
        error: errorDetails,
        context: 'executeIndexTasks',
        timestamp: new Date().toISOString()
      });

      // Report error metrics
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'execute_index_tasks_error',
        millis: Date.now(),
        extra3: errorDetails.type,
        extra4: errorDetails.message,
        extra5: this.dirPath,
        extra6: JSON.stringify(errorDetails)
      });
    } finally {
      this.isExecuting = false;
      if (this.isRepoBuilding) {
        this.isRepoBuilding = false;
        this.logger.info('Repo building completed');
      }
      if (!this.shouldCancel) {
        await this.updateProgress(dirPath, totalCount, totalCount, true, '');
      }
      this.logger.info('executeIndexTasks: Execution completed');
    }
  }
  private async processTasksWithQueues(
    tasksToIndex: any[],
    dirPath: string,
    totalCount: number,
    baseProcessed: number
  ): Promise<void> {
    const indexConfig = await this.httpClient.getIndexConfig();
    const QUEUE_COUNT = indexConfig?.['index-request-batch'] || 10; // 队列数量
    const FILES_PER_BATCH = indexConfig?.['file-index-batch'] || 10; // 每批文件数量

    // 准备文件数据
    const filesToIndex = tasksToIndex.map((task) => ({
      filepath: path.join(dirPath, task.filepath),
      action: task.file_action,
      task: task
    }));

    let fileIndex = 0; // 当前处理到的文件索引
    let completedTasks = 0; // 已完成的任务数
    const queuePromises: Promise<void>[] = []; // 队列处理Promise
    this.logger.debug(
      `Starting queue-based processing: ${filesToIndex.length} files, ${QUEUE_COUNT} queues, ${FILES_PER_BATCH} files per batch`
    );

    // 创建队列处理函数
    const processQueue = async (queueId: number): Promise<void> => {
      while (fileIndex < filesToIndex.length && this.status === 'executing' && !this.shouldCancel) {
        // 获取下一批文件
        const batchStartIndex = fileIndex;
        const batchEndIndex = Math.min(fileIndex + FILES_PER_BATCH, filesToIndex.length);

        if (batchStartIndex >= filesToIndex.length) {
          break;
        }

        // 更新索引，占用这些文件
        fileIndex = batchEndIndex;
        const batch = filesToIndex.slice(batchStartIndex, batchEndIndex);

        this.logger.debug(
          `Queue ${queueId}: Processing batch ${batchStartIndex}-${batchEndIndex - 1} (${batch.length} files)`
        );

        try {
          // 处理当前批次
          const batchFilesToIndex = batch.map((item) => ({
            filepath: item.filepath,
            action: item.action
          }));

          this.logger.debug(
            `Queue ${queueId}: Batch indexing ${batch.length} files with ${indexConfig['index-request-batch']} concurrent requests`
          );

          // 调用索引接口
          const result = await this.httpClient.indexFiles(batchFilesToIndex, dirPath);

          // 处理结果
          await this.processBatchResult(result, batch, dirPath);

          // 更新完成数量
          completedTasks += batch.length;

          // 更新进度
          if (this.isRepoBuilding) {
            await this.updateProgress(
              dirPath,
              totalCount,
              baseProcessed + completedTasks,
              true,
              batch[batch.length - 1]?.task?.filepath || ''
            );
          }

          this.logger.debug(
            `Queue ${queueId}: Completed batch, total completed: ${completedTasks}/${filesToIndex.length}`
          );
        } catch (error) {
          this.logger.error(`Queue ${queueId}: Error processing batch:`, error);

          // 处理失败的任务
          for (const item of batch) {
            try {
              await TaskTable.updateStatusByTaskId(
                item.task.dir_path,
                item.task.task_id,
                'pending',
                item.task.retry_count + 1 || 1
              );
            } catch (updateError) {
              this.logger.error(`Error updating failed task ${item.task.filepath}:`, updateError);
            }
          }

          completedTasks += batch.length;

          // 更新进度
          if (this.isRepoBuilding) {
            await this.updateProgress(
              dirPath,
              totalCount,
              baseProcessed + completedTasks,
              true,
              batch[batch.length - 1]?.task?.filepath || ''
            );
          }
        }
      }
    };

    // 启动所有队列
    for (let i = 0; i < QUEUE_COUNT; i++) {
      queuePromises.push(processQueue(i));
    }

    // 等待所有队列完成
    await Promise.all(queuePromises);

    this.logger.info(`Queue-based processing completed: ${completedTasks}/${filesToIndex.length} tasks processed`);
  }

  /**
   * 处理批次结果
   */
  private async processBatchResult(
    result: IndexUserFilesResponse,
    batch: Array<{ filepath: string; action: string; task: any }>,
    dirPath: string
  ): Promise<void> {
    // 批量更新成功的任务
    const successfulTaskIds: string[] = result.succeedFiles
      .map((file) => batch.find((item) => item.task.filepath === file.filepath)?.task.task_id ?? '')
      .filter((id) => id !== '');

    const failedTasks = result.failedFiles
      .map((file) => batch.find((item) => item.task.filepath === file.filepath)?.task)
      .filter((task) => task !== undefined);

    // 将失败的任务标记为失败并增加重试次数
    for (const task of failedTasks) {
      if (task) {
        try {
          await TaskTable.updateStatusByTaskId(task.dir_path, task.task_id, 'pending', task.retry_count + 1 || 1);
        } catch (updateError) {
          this.logger.error(`Error updating failed task ${task.filepath}:`, updateError);
        }
      }
    }

    // 批量删除成功的任务
    if (successfulTaskIds.length > 0) {
      await TaskTable.batchDeleteByTaskIds(
        dirPath,
        batch.map((item) => item.task.task_id)
      );
    }
  }
  async processFile(filePath: string, dirPath: string, action: IndexTaskAction, gitUrl: string): Promise<void> {
    try {
      // 使用正则表达式检查文件路径是否包含隐藏目录
      const hiddenDirPattern = /(\/|^)\.\w+/; // 匹配以 `.` 开头的目录或文件
      if (hiddenDirPattern.test(filePath)) {
        this.logger.info(`processFile: ${filePath} is in hidden directory, skipping`);
        return;
      }
      this.logger.info(`processFile: ${filePath}`);
      // 由于on事件不会等待 TaskTable.create 完成，所以需要先判断是否已经存在任务
      // if (this.recentProcessedFiles.includes(filePath)) {
      //   this.logger.info(`processFile: ${filePath} already processed`);
      //   return;
      // }
      // this.recentProcessedFiles.push(filePath);

      const existingTask = await TaskTable.findByFilePath(filePath);
      if (existingTask) {
        this.logger.info(`processFile: ${filePath} already exists`);
        return;
      }

      // 读取文件并计算hash
      // todo: 已经删除的文件无法进行 hash，这种要如何处理
      if (!fs.existsSync(path.resolve(dirPath, filePath))) {
        await this.addTask(filePath, dirPath, action, gitUrl);
        return;
      }

      this.logger.info(`processFile: ${filePath} add task`);
      await this.addTask(filePath, dirPath, action, gitUrl);
    } catch (error) {
      this.logger.error(`Error in processFile for ${filePath}:`, error);
    }
  }
  async checkFileCountValid(options: RepoIndexOptions, validFiles: number) {
    if (validFiles > MAX_INDEX_FILE_COUNT) {
      this.logger.error(
        `processRepo: ${options.dirPath} validFiles: ${validFiles}, 可以索引的文件数量超过 ${MAX_INDEX_FILE_COUNT}`
      );
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'process_repo_over_10k',
        millis: 1,
        extra3: validFiles.toString(),
        extra6: options.gitUrl
      });
      await RepoStatusTable.update(options.dirPath, {
        is_building: false,
        status: RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K
      });
      this.logger.reportUserAction({
        key: 'process_repo_over_10k',
        type: 'process_repo_over_10k',
        subType: options.gitUrl,
        content: JSON.stringify({
          repo: options.gitUrl,
          dirPath: options.dirPath
        })
      });
      await TaskTable.deleteByDirPath(options.dirPath);
      return false;
    } else {
      return true;
    }
  }

  async indexStructure(dirPath: string) {
    try {
      const structureText = generateDirectoryStructureText(dirPath);
      this.httpClient.indexFiles(
        [{ filepath: dirPath + '/项目根目录结构.txt', action: 'create', content: structureText }],
        dirPath
      );
    } catch (e) {
      this.logger.error(`processRepo: ${dirPath} error inserting repo structure: ${e}`);
    }
  }
  async processRepo(options: RepoIndexOptions) {
    try {
      this.logger.info(`processRepo: ${options.dirPath}`);
      try {
        await ConcurrencyLock.acquireLock(options.dirPath);
      } catch (error) {
        await this.sendProgress(
          0,
          100,
          1,
          '',
          IndexAction.ERROR,
          '存在其他IDE上打开了kwaipilot插件，暂时无法索引，请关闭插件后将自动继续索引'
        );
        this.logger.error(`processRepo: ${options.dirPath} is locked by other instance, skipping`);
        this.startExecute();
        return;
      }
      this.dirPath = options.dirPath;
      let repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
      this.shouldCancel = false;
      this.isRepoBuilding = true;
      // check git status with kconf
      const isGitRepo = await this.gitManager.isGitRepo(options.dirPath);
      if (!isGitRepo) {
        await this.sendProgress(0, 100, 1, '', IndexAction.ERROR, RepoStatusMessageMap[RepoStatusEnum.NOT_GIT_REPO]);
        this.logger.info(`processRepo: ${options.dirPath} is not a git repo, skipping`);
        return;
      }
      await this.sendProgress(0.01, 100, 1, '', IndexAction.PROCESS_FILE, '开始索引项目');
      // 判断是否为 git 仓库名单
      const gitSshUrl = convertToGitSshFormat(options.gitUrl);
      let repoCacheInfo: { commitId: string; branch: string } | null = null;
      if (!gitSshUrl) {
        this.logger.warn(`Failed to convert repo URL to git SSH format: ${gitSshUrl}`);
      } else {
        repoCacheInfo = (await this.httpClient.getLargeRepoIndexConfig(gitSshUrl)) as {
          commitId: string;
          branch: string;
        };
      }
      if (
        repoStatus?.status === RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_5K ||
        Boolean(repoStatus?.using_remote_index) !== true
      ) {
        // 如果之前是超过 5k的，则重新处理一遍。
        await this.clearIndex(options.dirPath, true);
        await SqliteDb.initTables();
        repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
      }
      if (
        repoCacheInfo &&
        (!repoStatus ||
          repoStatus.status === RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K ||
          repoStatus.status === RepoStatusEnum.LARGE_REPO_INIT)
      ) {
        this.logger.perf({
          namespace: INDEX_MANAGER_NAMESPACE,
          subtag: 'process_repo_use_remote_index',
          millis: Date.now(),
          extra3: options.dirPath,
          extra4: options.gitUrl,
          extra6: repoStatus ? 'update' : 'create'
        });
        if (!repoStatus) {
          await RepoStatusTable.create({
            dir_path: options.dirPath,
            commit_id: repoCacheInfo?.commitId,
            base_commit_id: repoCacheInfo?.commitId || undefined,
            created_at: Date.now(),
            updated_at: Date.now(),
            git_url: options.gitUrl,
            ide_version: options.ideVersion,
            plugin_version: options.pluginVersion,
            platform: options.platform as IdePlatform,
            total_files: 0,
            done_files: 0,
            is_building: true,
            status: RepoStatusEnum.LARGE_REPO_INIT,
            using_remote_index: true
          });
          repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
        } else {
          // 如果 basecommit 更新，那么该如何处理呢？
          // 使用git rev-list --ancestry-path a1b2c3d4..e5f6g7h8 排查两个 commit 的先后顺序
          // 移除掉两个 commit 之间的文件变更（diff 当前 commitid 和 basecommitId 之间的文件变更，遍历数据库中的文件，如果不在变更列表中，则删除。）
          // 文件删除之前记录状态，防止出现状态改变但是未删除完的情况，用户关闭 ide
          await RepoStatusTable.update(options.dirPath, {
            status: RepoStatusEnum.LARGE_REPO_INIT,
            base_commit_id: repoCacheInfo?.commitId || undefined
          });
          repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
        }
      }
      if (!repoStatus) {
        const allFiles = await this.gitManager.getAllFiles(options.dirPath);
        const totalFiles = allFiles.length;
        await RepoStatusTable.create({
          dir_path: options.dirPath,
          commit_id: options.commitId,
          base_commit_id: repoCacheInfo?.commitId || undefined,
          created_at: Date.now(),
          updated_at: Date.now(),
          git_url: options.gitUrl,
          ide_version: options.ideVersion,
          plugin_version: options.pluginVersion,
          platform: options.platform as IdePlatform,
          total_files: totalFiles,
          done_files: 0,
          is_building: true,
          status: RepoStatusEnum.SUCCESS,
          using_remote_index: true
        });
        let validFiles = 0;
        let doneFiles = 0;
        const BATCH_SIZE = 200; // 减小批次大小
        const DELAY_BETWEEN_BATCHES = 100; // 批次间延迟(ms)
        const MAX_TASKS_IN_MEMORY = 1000; // 内存中最大任务数量
        let validTasks = [];
        await this.indexStructure(options.dirPath);
        // Process files in batches
        for (let i = 0; i < allFiles.length; i += BATCH_SIZE) {
          if (this.shouldCancel) {
            this.logger.info('File processing cancelled');
            break;
          }
          const batchFiles = allFiles.slice(i, Math.min(i + BATCH_SIZE, allFiles.length));

          // 并行处理当前批次的文件
          const batchResults = await Promise.all(
            batchFiles.map(async (filePath) => {
              doneFiles++;

              // 详细检查
              if (!(await this.checkIfNeedAddTask(filePath, options.dirPath))) {
                return null;
              }

              return {
                task_id: nanoid(),
                filepath: filePath,
                dir_path: options.dirPath,
                git_url: options.gitUrl,
                created_at: Date.now(),
                updated_at: Date.now(),
                status: 'pending' as const,
                file_action: 'create' as const,
                retry_count: 0
              };
            })
          );
          // 过滤出有效的任务
          const newValidTasks = batchResults.filter((task): task is NonNullable<typeof task> => task !== null);
          validTasks.push(...newValidTasks);
          // 如果累积的任务达到阈值，批量创建并清空
          if (validTasks.length >= MAX_TASKS_IN_MEMORY) {
            await TaskTable.batchCreate(validTasks);
            validFiles += validTasks.length;
            validTasks = [];
          }

          // 添加延迟，避免系统过载
          await new Promise((resolve) => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
        }

        // 处理剩余的任务
        if (validTasks.length > 0) {
          validFiles += validTasks.length;
          await TaskTable.batchCreate(validTasks);
        }
        // 非手动情况下，检查文件数量是否超过 10k
        if (!options.manual) {
          const valid = await this.checkFileCountValid(options, validFiles);
          if (!valid) {
            await this.sendProgress(
              0,
              100,
              1,
              '',
              IndexAction.ERROR,
              `${RepoStatusMessageMap[RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K]}, 当前文件数量: ${validFiles}`
            );
            return;
          }
        }

        await RepoStatusTable.update(options.dirPath, {
          is_building: true,
          total_files: validFiles,
          status: RepoStatusEnum.SUCCESS
        });
      } else if (repoStatus.commit_id !== options.commitId) {
        // 判断如果找不到 commitId，提示用户执行 git pull
        let changedFiles: Array<{ path: string; status: IndexTaskAction }> = [];
        try {
          // 获取两个 commit 之间的变更文件
          const commitChangedFiles = await this.gitManager.getChangedFilesBetweenCommits(
            options.dirPath,
            options.commitId,
            repoStatus.commit_id || repoCacheInfo?.commitId || ''
          );

          // 获取用户本地变更文件
          const userChangedFiles = await this.gitManager.getUserChangedFiles(options.dirPath);
          // 合并两种变更文件并去重
          const allChanges = [...commitChangedFiles, ...userChangedFiles];
          const fileMap = new Map<string, { path: string; status: IndexTaskAction }>();

          // 使用 Map 去重，保留优先级最高的状态 (delete > create > modify)
          const statusPriority: Record<IndexTaskAction, number> = {
            delete: 0,
            create: 1,
            modify: 2
          };
          allChanges.forEach((change) => {
            const existing = fileMap.get(change.path);
            if (!existing || statusPriority[change.status] < statusPriority[existing.status]) {
              fileMap.set(change.path, change);
            }
          });

          changedFiles = Array.from(fileMap.values());
          this.logger.perf({
            namespace: INDEX_MANAGER_NAMESPACE,
            subtag: 'process_repo_get_changed_files',
            millis: Date.now(),
            extra3: options.dirPath,
            extra4: options.gitUrl,
            extra6: changedFiles.length.toString()
          });
        } catch (error) {
          // 如果当前设置的 commitId 与 base 中的 commitid 一致，并且执行报错了，那么认为是本地无法获取到最新 commitid 信息，提示用户执行 git pull 命令
          if (repoCacheInfo?.commitId && repoStatus.commit_id === repoCacheInfo.commitId) {
            this.logger.error(`processRepo: ${options.dirPath} error getting changed files: ${error}`);
            await this.sendProgress(0, 100, 1, '', IndexAction.ERROR, 'git 仓库异常，无法拉取到最新 commit信息');
            this.messenger.send('state/sendNotification', {
              type: 'warning',
              name: 'GIT_DIFF_ERROR',
              message: `索引更新异常，当前无法拉取到最新commit信息，请执行 git pull 命令`
            });
            // 上报
            this.logger.perf({
              namespace: INDEX_MANAGER_NAMESPACE,
              subtag: 'git_diff_error',
              millis: Date.now(),
              extra6: `repoStatus.commit_id: ${repoStatus.commit_id},options.commitId: ${options.commitId},repoCacheInfo?.commitId: ${repoCacheInfo?.commitId}`
            });
            this.startExecute();
            return;
          }
        }
        let doneFiles = 0;
        for (const { path: filePath, status } of changedFiles) {
          await this.processFile(filePath, options.dirPath, status, options.gitUrl);
          doneFiles++;
        }

        // 获取当前进度
        const pendingTasks = await TaskTable.findByStatus(options.dirPath, 'pending');
        const indexingTasks = await TaskTable.findByStatus(options.dirPath, 'indexing');

        await RepoStatusTable.update(options.dirPath, {
          commit_id: options.commitId,
          updated_at: Date.now(),
          ide_version: options.ideVersion,
          plugin_version: options.pluginVersion,
          platform: options.platform as IdePlatform,
          // todo: total_files: totalFiles,
          // todo: done_files: indexedLen,
          is_building: true
        });
      }
      if (repoStatus && repoStatus?.status === RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K) {
        if (options.manual) {
          await this.clearIndex(options.dirPath, true);
          await this.processRepo(options);
          return;
        }
        await this.sendProgress(
          0,
          100,
          1,
          '',
          IndexAction.ERROR,
          RepoStatusMessageMap[RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K]
        );
        this.logger.info(`processRepo: ${options.dirPath} repo status is 1, skipping`);
        this.logger.perf({
          namespace: INDEX_MANAGER_NAMESPACE,
          subtag: 'process_repo_over_10k',
          millis: 1,
          extra3: options.dirPath,
          extra6: options.gitUrl
        });
        this.logger.reportUserAction({
          key: 'process_repo_over_10k',
          type: 'process_repo_over_10k',
          subType: options.gitUrl,
          content: JSON.stringify({
            repo: options.gitUrl,
            dirPath: options.dirPath
          })
        });
        return;
      }
      this.updateRepoInfo();
      this.startExecute();
    } catch (error) {
      const errorDetails = {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        code: (error as any)?.code,
        type: error?.constructor?.name,
        dirPath: options?.dirPath,
        gitUrl: options?.gitUrl,
        commitId: options?.commitId,
        platform: options?.platform
      };
      this.logger.error('Error in processRepo:', {
        error: errorDetails,
        context: 'processRepo',
        timestamp: new Date().toISOString()
      });

      // Report error metrics
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'process_repo_error',
        millis: Date.now(),
        extra3: errorDetails.type,
        extra4: errorDetails.message,
        extra5: options?.dirPath,
        extra6: JSON.stringify(errorDetails)
      });
    }
  }
  private async updateUserChangedFile() {
    // 获取用户本地变更文件
    const userChangedFiles = await this.gitManager.getUserChangedFiles(this._dirPath);
    if (userChangedFiles.length === 0) {
      return;
    }
    const gitUrl = GlobalConfig.getConfig().getRepoPath();
    for (const { path: filePath, status } of userChangedFiles) {
      await this.processFile(filePath, this.dirPath, status, gitUrl);
    }
  }
  private async updateRepoInfo() {
    const dirPath = this.dirPath || GlobalConfig.getConfig().getRepoPath();
    const repoStatus = await RepoStatusTable.findByDirPath(dirPath);
    if (repoStatus) {
      await this.httpClient.updateRepoInfo({
        userId: GlobalConfig.getConfig().getUsername(),
        repoUrl: repoStatus.git_url,
        deviceId: GlobalConfig.getConfig().getDeviceId(),
        ideVersion: repoStatus.ide_version,
        dirPath: dirPath,
        branch: repoStatus.branch,
        commitId: repoStatus.commit_id,
        baseCommitId: repoStatus.base_commit_id
      });
    }
  }

  public async getRepoBuildStatus(): Promise<{
    id: number;
    repo: string;
    repoPath: string;
    branch: string;
    commitId: string;
    lastUpdateTime: number;
    createTime: number;
    total: number;
    done: number;
    progress: number;
    isBuilding: boolean;
    status: RepoStatusEnum;
    message: string;
    isPaused: boolean;
  } | null> {
    try {
      const [repoStatus] = await Promise.all([RepoStatusTable.findByDirPath(this.dirPath)]);
      if (!repoStatus) {
        return null;
      }

      return {
        id: repoStatus.id,
        repo: repoStatus.git_url,
        repoPath: repoStatus.dir_path,
        branch: repoStatus.branch ?? '',
        commitId: repoStatus.commit_id,
        lastUpdateTime: repoStatus.updated_at,
        createTime: repoStatus.created_at,
        total: repoStatus.total_files,
        done: repoStatus.done_files,
        progress: repoStatus.total_files > 0 ? repoStatus.done_files / repoStatus.total_files : 1,
        isBuilding: repoStatus.is_building,
        status: repoStatus.status,
        isPaused: false, // todo:userStatus?.status === 0
        message: RepoStatusMessageMap[repoStatus.status]
      };
    } catch (error) {
      const errorDetails = {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        code: (error as any)?.code,
        type: error?.constructor?.name,
        dirPath: this.dirPath
      };
      this.logger.error('Error in getRepoBuildStatus:', {
        error: errorDetails,
        context: 'getRepoBuildStatus',
        timestamp: new Date().toISOString()
      });
      // Report error metrics
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'get_repo_build_status_error',
        millis: Date.now(),
        extra3: errorDetails.type,
        extra4: errorDetails.message,
        extra5: this.dirPath,
        extra6: JSON.stringify(errorDetails)
      });
      return null;
    }
  }
  public async clearIndex(dirPath?: string, isForce: boolean = false) {
    if (!dirPath) {
      return false;
    }
    // 清除掉远端的索引
    await this.httpClient.clearIndexForUser();
    if (isForce) {
      // 删除 index.sqlite 文件

      try {
        const sqliteDir = getSqlitePath(dirPath);
        if (fs.existsSync(sqliteDir)) {
          await fsPromises.rm(sqliteDir, { recursive: true, force: true });
        }
        this.logger.info(`Deleted sqlite file: ${sqliteDir}`);
      } catch (e) {
        this.logger.warn(`Failed to delete sqlite file for ${dirPath}:`, e);
      } // 删除 lancedb 目录
      try {
        const lanceDbRoot = getLanceDbPath();
        const collectionName = generateCollectionName(dirPath);
        const cacheCollectionName = getCacheCollectionName(collectionName);
        const lanceDbDir = path.join(lanceDbRoot, collectionName) + '.lance';
        const cacheLanceDbDir = path.join(lanceDbRoot, cacheCollectionName) + '.lance';
        if (fs.existsSync(lanceDbDir)) {
          await fsPromises.rm(lanceDbDir, { recursive: true, force: true });
        }
        if (fs.existsSync(cacheLanceDbDir)) {
          await fsPromises.rm(cacheLanceDbDir, { recursive: true, force: true });
        }
        this.logger.info(`Deleted lancedb dir: ${lanceDbDir}`);
      } catch (e) {
        this.logger.warn(`Failed to delete lancedb dir for ${dirPath}:`, e);
      }
      return true;
    }

    try {
      this.logger.info(`Clearing index for ${dirPath}`);
      if (!dirPath) {
        dirPath = this.dirPath;
      }
      this.shouldCancel = true;
      // 清除数据库中的记录
      await TaskTable.deleteByDirPath(dirPath);
      await RepoStatusTable.deleteByDirPath(dirPath);

      // this.recentProcessedFiles = [];
      // 重置状态
      this.isRepoBuilding = false;
      return await clearIndex(dirPath);
    } catch (error) {
      this.logger.error('Error in clearIndex:', error);
      return false;
    }
  }
  public async dispose() {
    try {
      if (this.autoExecuteTimer) {
        clearTimeout(this.autoExecuteTimer);
        this.autoExecuteTimer = null;
      }
      this.status = 'paused';
    } catch (error) {
      this.logger.error('Error in dispose:', error);
    }
  }

  public async initEmbeddingList() {
    try {
      const BATCH_SIZE = 200;
      let offset = 0;
      let isFirstBatch = true;
      const filename = path.join(this.dirPath, 'embedding_list.txt');
      while (true) {
        // todo: 这里要改成是从云端获取
        // const batch = await FileStatusTable.findByDirPathPaged(this.dirPath, BATCH_SIZE, offset);
        // if (!batch || batch.length === 0) break;
        // const fileList = batch.map((f) => f.filepath).join('\n');
        // if (isFirstBatch) {
        //   await fs.promises.writeFile(filename, fileList + '\n', 'utf-8');
        //   isFirstBatch = false;
        // } else {
        //   await fs.promises.appendFile(filename, fileList + '\n', 'utf-8');
        // }
        // if (batch.length < BATCH_SIZE) break;
        // offset += BATCH_SIZE;
      }
    } catch (error) {
      this.logger.error('Error in initEmbeddingList:', error);
    }
  }
}
