import { TaskTable, FileStatusTable, UserStatusTable, RepoStatusTable } from '@/db/sqlite/tables/index';
import { clearIndex, indexFile } from '@/indexing';
import { IdePlatform, IndexAction } from '@/protocol/ideCore';
import { nanoid } from 'nanoid';
import { I<PERSON><PERSON>enger } from '@/protocol/messenger';
import type { ToCoreProtocol, FromCoreProtocol } from '@/protocol';
import { Logger } from '@/util/log';
import { AGENT_NAMESPACE, generateCollectionName, getCacheCollectionName, MAX_INDEX_FILE_COUNT } from '@/util/const';
import path from 'path';
import { CodeEmbedding } from '@/embedding/CodeEmbedding';
import { FileSystemHelper } from './FileSystemHelper';
import { CacheManager } from './CacheManager';
import { GitManager } from './GitManager';
import { IndexOptions, IndexTaskAction, RepoIndexOptions } from './types';
import fs from 'fs';
import { LanceDB } from '@/db/lancedb';
import { PROGRAM_FILES } from '@/code/analyze/code_utils';
import { filename_to_lang } from '@/code/chunk/code';
import { getDiskUsed } from '@/util/log-utils';
import { getLanceDbPath, getIndexFolderPath, getSqlitePath } from '@/util/paths';
import { GlobalConfig } from '@/util/global';
import { RepoStatusEnum } from '@/db/sqlite/tables/repo-status';
import { RepoStatusMessageMap } from '@/db/sqlite/tables/repo-status';
import ConcurrencyLock from './ConcurrencyLock';
import { SqliteDb } from '@/db/sqlite';
import { isTextFile } from './utils';
import { Api } from '@/http';
import { convertToGitSshFormat } from '@/util/git-utils';
import type { RepoStatus } from '@/db/sqlite/tables/repo-status';
import { open } from 'sqlite';
import fsPromises from 'fs/promises';
import { loadSqlite3 } from '@/util/mod';

export enum IndexTaskStatus {
  PENDING = 'pending',
  INDEXING = 'indexing'
}
const INDEX_MANAGER_NAMESPACE = 'index_manager';
export interface IndexTask {
  id: string;
  filepath: string;
  repoDir: string;
  status: IndexTaskStatus;
  fileAction: IndexTaskAction;
  createdAt: number;
  updatedAt: number;
}
export class IndexManager {
  private readonly logger: Logger = new Logger('IndexManager');
  private readonly fileSystem: FileSystemHelper;
  private readonly gitManager: GitManager;
  private readonly fileHashCache: CacheManager<string>;
  private readonly indexCache: CacheManager<Record<string, unknown>>;

  private autoExecuteTimer: NodeJS.Timeout | null = null;
  // 用户暂停构建时使用这个字段判断当前状态
  private status: 'idle' | 'executing' | 'paused' = 'idle';
  // 定时任务是否正在执行，防止本次未执行完，下次已经开始。
  private isExecuting: boolean = false;
  // 是否取消当前任务,构建过程中，如果用户手动停止，则设置为 true
  private shouldCancel: boolean = false;
  private _dirPath: string = '';
  // 是否是 repoindex，如果是则发送通知消息
  private isRepoBuilding = false;
  // 这个地方不需要的了，容易会出现已经处理过的文件，更改之后，不会被索引。
  // private recentProcessedFiles: string[] = [];
  private timer: number = 5 * 60 * 1000;
  private httpClient = new Api();

  constructor(
    private readonly messenger: IMessenger<ToCoreProtocol, FromCoreProtocol>,
    private readonly options: IndexOptions = {
      maxFileSize: 1 * 1024 * 1024, // 1M
      batchSize: 30,
      delayPerFile: 100,
      delayPerBatch: 1000,
      maxCacheSize: 10000,
      cacheTTL: 24 * 60 * 60 * 1000
    }
  ) {
    this.fileSystem = new FileSystemHelper();
    this.gitManager = new GitManager();
    this.fileHashCache = new CacheManager<string>(options.maxCacheSize, options.cacheTTL, 'FileHash');
    this.indexCache = new CacheManager<Record<string, unknown>>(options.maxCacheSize, options.cacheTTL, 'Index');
  }

  get dirPath() {
    if (!this._dirPath) {
      this.logger.error('dirPath is not defined');
      return '';
    }
    return this._dirPath;
  }
  set dirPath(dirPath: string) {
    if (!dirPath) {
      this.logger.error('dirPath is required');
      return;
    }
    if (dirPath === this._dirPath) return;
    this.logger.error(`workspace dirPath changed, stop current task, ${this._dirPath} -> ${dirPath}`);
    // 停止当前正在执行的任务
    this.shouldCancel = true;
    this.status = 'paused';
    // 等待当前任务完成
    const waitForTaskToFinish = async () => {
      while (this.isExecuting) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
    };

    // 清理当前状态
    const cleanup = async () => {
      // 等待任务完成
      await waitForTaskToFinish();

      // 清理定时器
      if (this.autoExecuteTimer) {
        clearTimeout(this.autoExecuteTimer);
        this.autoExecuteTimer = null;
      }

      // 重置所有状态
      this.shouldCancel = false;
      this.isExecuting = false;
      this.status = 'idle';
      this.isRepoBuilding = false;
      // this.recentProcessedFiles = [];

      // 清理缓存
      this.fileHashCache.cleanup();
      this.indexCache.cleanup();
    };

    cleanup().then(() => {
      this._dirPath = dirPath;
      // this.init();
    });
  }

  /**
   * 初始化 获取用户是否手动停止
   */
  private async init() {
    try {
      const userStatus = await UserStatusTable.findByDirPath(this.dirPath);
      if (userStatus) {
        this.status = userStatus.status === 0 ? 'paused' : this.status;
      }
    } catch (error) {
      this.logger.error('Error in init:', error);
    }
  }

  /**
   * 用户手动停止构建
   */
  async pauseExecute() {
    try {
      this.shouldCancel = true;
      if (this.status === 'paused') {
        this.logger.info('pauseExecute: already paused');
        return false;
      }
      this.status = 'paused';
      if (this.autoExecuteTimer) {
        clearTimeout(this.autoExecuteTimer);
        this.autoExecuteTimer = null;
      }
      await UserStatusTable.update(this.dirPath, {
        status: 0
      });
      return true;
    } catch (error) {
      this.logger.error('Error in pauseExecute:', error);
      return false;
    }
  }

  /**
   * 用户手动执行构建
   */
  async startExecute(): Promise<boolean> {
    let logCPUInfoInterval: NodeJS.Timeout | null = null;
    try {
      if (this.status === 'executing') {
        this.logger.info('startExecute: already executing');
        return false;
      }
      this.status = 'executing';
      await UserStatusTable.update(this.dirPath, {
        status: 1
      });
      logCPUInfoInterval = setInterval(() => {
        this.logger.logCPUInfo(INDEX_MANAGER_NAMESPACE, 'startExecute');
      }, 1000);
      this.executeIndexTasks(this.dirPath).then(() => {
        if (logCPUInfoInterval) {
          clearInterval(logCPUInfoInterval);
        }
        this.status = 'idle';
        this.scheduleNextExecution();
      });
      return true;
    } catch (error) {
      if (logCPUInfoInterval) {
        clearInterval(logCPUInfoInterval);
      }
      this.logger.error('Error in startExecute:', error);
      return false;
    }
  }

  private async checkIfNeedAddTask(filePath: string, dirPath: string): Promise<boolean> {
    try {
      if (filePath.toLowerCase().indexOf('readme') > -1) {
        return true;
      }

      const lang = filename_to_lang(filePath);
      if (!lang || !PROGRAM_FILES.includes(lang)) {
        return false;
      }

      const existTask = await TaskTable.findByFilePath(filePath);
      if (existTask) {
        return false;
      }

      // 检查是否为文本文件
      if (!(await isTextFile(filePath, dirPath))) {
        return false;
      }
      // 判断文件大小，是否超过 1M
      const fullPath = path.join(dirPath, filePath);
      const fileSize = await this.fileSystem.getFileSize(fullPath);
      if (fileSize > this.options.maxFileSize) {
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error(`Error checking if file needs to be added to index: ${filePath}`, error);
      return false;
    }
  }
  /**
   * 真正添加文件到 【待索引区】
   */
  private async addTask(filePath: string, dirPath: string, action: IndexTaskAction, gitUrl: string): Promise<void> {
    try {
      if (!(await this.checkIfNeedAddTask(filePath, dirPath))) {
        return;
      }

      this.logger.info(`Adding task for file: ${filePath}`);
      const taskId = nanoid();
      await TaskTable.create({
        filepath: filePath,
        dir_path: dirPath,
        file_action: action,
        created_at: Date.now(),
        updated_at: Date.now(),
        task_id: taskId,
        git_url: gitUrl,
        status: 'pending',
        retry_count: 0
      });
    } catch (error) {
      this.logger.error(`Error adding task for file ${filePath}:`, error);
      // 不抛出错误，继续处理其他文件
    }
  }

  /**
   * 定时执行
   */
  private async scheduleNextExecution() {
    this.logger.info('scheduleNextExecution start');
    if (this.status === 'paused') {
      this.logger.info('Index execution is paused, skipping schedule');
      return;
    }

    // 清理之前的定时器
    if (this.autoExecuteTimer) {
      this.logger.info('Clearing previous timer');
      clearTimeout(this.autoExecuteTimer);
      this.autoExecuteTimer = null;
    }

    // 如果当前有任务在执行，等待其完成
    if (this.isExecuting) {
      this.logger.info('Task is currently executing, waiting for completion');
      this.shouldCancel = true;
      while (this.isExecuting) {
        await new Promise((resolve) => setTimeout(resolve, 100));
      }
      this.shouldCancel = false;
    }

    // 检查是否有待处理的任务
    const pendingTasks = await TaskTable.countTasks(this.dirPath, ['pending', 'indexing']);
    this.logger.info(`Found ${pendingTasks} pending/indexing tasks`);

    if (pendingTasks === 0) {
      this.logger.info('No pending tasks, scheduling next check');
      this.autoExecuteTimer = setTimeout(() => this.scheduleNextExecution(), this.timer);
      return;
    }
    const logCPUInfoInterval = setInterval(() => {
      this.logger.logCPUInfo(INDEX_MANAGER_NAMESPACE, 'scheduleNextExecution');
    }, 1000);
    try {
      this.status = 'executing';
      this.logger.info(`Starting to execute ${pendingTasks} tasks`);
      await this.executeIndexTasks(this.dirPath);
      this.logger.info('Task execution completed successfully');
    } catch (error) {
      this.logger.error('Error during task execution:', error);
    } finally {
      if (logCPUInfoInterval) {
        clearInterval(logCPUInfoInterval);
      }
      if (this.status === 'executing') {
        // 只有在执行状态下才改变状态和调度下一次执行
        this.status = 'idle';
        this.logger.info('Scheduling next execution');
        this.autoExecuteTimer = setTimeout(() => this.scheduleNextExecution(), this.timer);
      } else {
        this.logger.info(`Not scheduling next execution, current status: ${this.status}`);
      }
    }
  }

  /**
   * 检查文件是否需要重新索引
   */
  private async shouldReindex(filePath: string): Promise<boolean> {
    const fullPath = path.join(this.dirPath, filePath);
    try {
      if (!fs.existsSync(fullPath)) {
        return true;
      }
      const currentHash = await this.fileSystem.getFileHash(fullPath);
      const cachedHash = this.fileHashCache.get(filePath);

      if (!cachedHash) {
        this.fileHashCache.set(filePath, currentHash);
        return true;
      }
      if (cachedHash !== currentHash) {
        this.fileHashCache.set(filePath, currentHash);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Error checking file ${filePath}:`, error);
      return true;
    }
  }

  /**
   * 更新进度并通知前端
   */
  private async updateProgress(
    dirPath: string,
    totalFiles: number,
    doneFiles: number,
    isBuilding: boolean,
    currentFile: string = '',
    action: IndexAction = IndexAction.INDEX_FILE
  ) {
    try {
      await RepoStatusTable.updateProgress(dirPath, totalFiles, doneFiles, isBuilding);
      this.sendProgress(
        totalFiles > 0 ? Math.max(doneFiles / totalFiles, 0.01) : 1, // 0.01 表示最小进度
        totalFiles,
        doneFiles,
        currentFile,
        action,
        currentFile ? `索引中: ${currentFile}` : ''
      );
    } catch (error) {
      this.logger.error('Error in updateProgress:', error);
    }
  }
  private async sendProgress(
    progress: number,
    totalFiles: number,
    doneFiles: number,
    currentFile: string = '',
    action: IndexAction = IndexAction.INDEX_FILE, // 加一个 error 的 action
    message: string = ''
  ) {
    try {
      // 发送进度到前端
      this.messenger.send('index/progress', {
        progress,
        total: totalFiles,
        done: doneFiles,
        filepath: currentFile,
        action: action,
        message: message
      });
    } catch (error) {
      this.logger.error('Error in sendProgress:', error);
    }
  }

  private async checkLanceDbDiskSpace() {
    try {
      // 获取LanceDB目录的磁盘使用情况
      const lanceDbPath = getLanceDbPath();
      const diskUsage = await getDiskUsed(lanceDbPath);
      const MAX_INDEX_SIZE = await GlobalConfig.getConfig().getMaxIndexSpace();
      if (!diskUsage) {
        this.logger.error('Failed to get LanceDB disk usage information');
        return false;
      }
      this.logger.info(
        `Current LanceDB disk usage: ${diskUsage} bytes (${(diskUsage / MAX_INDEX_SIZE).toFixed(2)} GB)`
      ); // 如果磁盘使用超过1GB，清理旧项目
      if (diskUsage > MAX_INDEX_SIZE) {
        this.logger.warn(
          `LanceDB disk usage exceeds 1GB (${(diskUsage / MAX_INDEX_SIZE).toFixed(2)} GB), cleaning up...`
        );
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Error checking LanceDB disk space:', error);
      return false;
    }
  }

  /**
   * 检查磁盘空间，如果超过1GB则清理旧项目
   * @returns 返回清理后的磁盘空间大小（字节）
   */
  private async checkDiskSpace(): Promise<void> {
    try {
      const needClean = await this.checkLanceDbDiskSpace();

      if (needClean) {
        // 查询各 index.sqlite 中的 repo 信息
        const indexFolderPath = path.join(getIndexFolderPath(), 'sqlite');
        const folders = await fs.promises.readdir(indexFolderPath);
        const indexFilesPath = await Promise.all(
          folders.map(async (folder) => {
            const dist = path.join(indexFolderPath, folder);
            // 判断dist 是不是目录
            const isDir = await fs.promises.stat(dist).then((stat) => stat.isDirectory());
            if (isDir) {
              const files = await fs.promises.readdir(dist);
              if (files.includes('index.sqlite')) {
                return path.join(indexFolderPath, folder, 'index.sqlite');
              }
            }
            return '';
          })
        );
        const indexFiles = indexFilesPath.filter((file) => file !== '');
        let allRepos: RepoStatus[] = [];
        for (const file of indexFiles) {
          try {
            if (!fs.existsSync(file)) {
              continue;
            }
            const filePath = file;
            const sqlite3 = await loadSqlite3();
            const db = await open({ filename: filePath, driver: sqlite3.Database });
            const repos = await db.all<RepoStatus[]>('SELECT * FROM repo_status ORDER BY updated_at ASC');
            allRepos = allRepos.concat(repos);
            await db.close();
          } catch (error) {
            this.logger.error(`Failed to read repo info from ${file}:`, error);
          }
        }

        // 按更新时间排序
        allRepos.sort((a, b) => a.updated_at - b.updated_at);

        for (const repo of allRepos) {
          if (repo.dir_path === this.dirPath) {
            continue;
          }
          this.messenger.send('state/sendNotification', {
            type: 'warning',
            name: 'DB_NOT_ENOUGH',
            message: `磁盘空间不足，将清理旧项目索引，项目名称：${repo.dir_path}`
          });
          await this.clearIndex(repo.dir_path, true);
          const check = await this.checkLanceDbDiskSpace();
          if (!check) {
            break;
          }
        }
      }
    } catch (error) {
      this.logger.error('Error checking LanceDB disk space:', error);
    }
  }

  /**
   * 优化后的执行索引任务方法
   */
  private async executeIndexTasks(dirPath: string) {
    // 执行之前首先判断是否被其他实例锁定
    try {
      await ConcurrencyLock.acquireLock(this.dirPath);
    } catch (error) {
      this.logger.error('Error acquiring lock:', error);
      return;
    }
    const startExecuteTime = Date.now();
    // 增加判断磁盘空间的逻辑，如果超过 1G，则删除之前构建的项目
    await this.checkDiskSpace();

    this.logger.info('executeIndexTasks: Starting execution');
    if (this.status !== 'executing') {
      this.logger.info('Index execution is not in executing state');
      return;
    }

    this.isExecuting = true;
    let totalCount = 0;

    try {
      this.logger.info('Cleaning up caches');
      this.fileHashCache.cleanup();
      this.indexCache.cleanup();

      const BATCH_SIZE = 100;
      let lastProcessedId = 0; // 用于追踪最后处理的任务ID
      let totalProcessed = 0;
      let hasMoreTasks = true;

      totalCount = await TaskTable.countTasks(dirPath, ['pending', 'indexing']);
      this.logger.info(`Total tasks to process: ${totalCount}`);

      if (totalCount === 0) {
        this.logger.info('No pending or indexing tasks');
        return;
      }

      while (hasMoreTasks && this.status === 'executing' && !this.shouldCancel) {
        this.logger.debug(`Fetching next batch of tasks after ID ${lastProcessedId}`);
        // 修改为基于ID的分页查询
        const tasks = await TaskTable.findTasksAfterIdWithLimit(
          dirPath,
          ['pending', 'indexing'],
          lastProcessedId,
          BATCH_SIZE
        );

        this.logger.info(`Retrieved ${tasks.length} tasks in current batch`);

        if (tasks.length === 0) {
          this.logger.info('No more tasks to process');
          hasMoreTasks = false;
          break;
        }

        // 更新最后处理的ID
        lastProcessedId = tasks[tasks.length - 1]?.id ?? 0;
        this.logger.debug(`Last processed ID updated to ${lastProcessedId}`);

        // 顺序处理每个任务
        for (const task of tasks) {
          // 检查是否需要取消
          if (this.shouldCancel || this.status !== 'executing') {
            this.logger.info('Task execution cancelled');
            return;
          }
          // 24 小时内重试 3 次
          if (task.retry_count && task.retry_count >= 3 && Date.now() - task.updated_at < 1000 * 60 * 60 * 24) {
            this.logger.info(`Task ${task.filepath} retry count exceeds 3, skipping`);
            continue;
          }
          // 超过 24 小时则重置重试次数
          if (Date.now() - task.updated_at > 1000 * 60 * 60 * 24) {
            task.retry_count = 0;
          }
          this.logger.debug(`Processing task: ${task.filepath}`);
          try {
            if (!(await this.shouldReindex(task.filepath))) {
              totalProcessed++;
              this.logger.debug(`File unchanged, skipping: ${task.filepath} (${totalProcessed}/${totalCount})`);
              if (this.isRepoBuilding) {
                await this.updateProgress(dirPath, totalCount, totalProcessed, true, task.filepath);
              }
              continue;
            }
            this.logger.debug(`Updating task status to indexing: ${task.filepath}`);
            await TaskTable.updateStatusByTaskId(task.dir_path, task.task_id, 'indexing', task.retry_count || 0);

            const cacheKey = `${task.filepath}:${task.file_action}`;
            const cachedIndex = this.indexCache.get(cacheKey);
            if (cachedIndex) {
              totalProcessed++;
              this.logger.debug(`Using cached index for: ${task.filepath} (${totalProcessed}/${totalCount})`);
              if (this.isRepoBuilding) {
                await this.updateProgress(dirPath, totalCount, totalProcessed, true, task.filepath);
              }
              continue;
            }

            this.logger.debug(`Indexing file: ${task.filepath}`);
            const fullPath = path.join(dirPath, task.filepath);
            await indexFile(fullPath, task.dir_path, task.file_action);

            this.indexCache.set(cacheKey, {});
            this.logger.debug(`File indexed successfully: ${task.filepath}`);

            await FileStatusTable.update(task.filepath, {
              filepath: task.filepath,
              dir_path: task.dir_path,
              status: 'indexed',
              updated_at: Date.now(),
              hash: await this.fileSystem.getFileHash(fullPath),
              git_url: task.git_url
            });

            await TaskTable.deleteByTaskId(task.dir_path, task.task_id);
            this.logger.debug(`Task completed and removed: ${task.filepath}`);

            totalProcessed++;
            if (this.isRepoBuilding) {
              await this.updateProgress(dirPath, totalCount, totalProcessed, true, task.filepath);
            }

            // 强制进行垃圾回收
            if (totalProcessed % 1000 === 0) {
              this.logger.debug('Triggering garbage collection');
              global.gc?.();
            }
            try {
              if (process.arch !== 'arm64') {
                await new Promise((resolve) => setTimeout(resolve, this.options.delayPerFile));
              }
            } catch (e) {
              await new Promise((resolve) => setTimeout(resolve, this.options.delayPerFile));
            }
          } catch (error) {
            // this.fileHashCache.delete(task.filepath);
            const errorDetails = {
              message: error instanceof Error ? error.message : String(error),
              stack: error instanceof Error ? error.stack : undefined,
              code: (error as any)?.code,
              type: error?.constructor?.name,
              dirPath: this.dirPath,
              status: this.status,
              isExecuting: this.isExecuting,
              totalCount
            };

            this.logger.error('Error executing index tasks---1111:', {
              error: errorDetails,
              context: 'executeIndexTasks',
              timestamp: new Date().toISOString()
            });

            // Report error metrics
            this.logger.perf({
              namespace: INDEX_MANAGER_NAMESPACE,
              subtag: 'execute_index_tasks_error',
              millis: Date.now(),
              extra3: errorDetails.type,
              extra4: errorDetails.message,
              extra5: this.dirPath,
              extra6: JSON.stringify(errorDetails)
            });

            await TaskTable.updateStatusByTaskId(task.dir_path, task.task_id, 'pending', task.retry_count + 1 || 1);
            totalProcessed++;
            if (this.isRepoBuilding) {
              await this.updateProgress(dirPath, totalCount, totalProcessed, true, task.filepath);
            }
            await FileStatusTable.update(task.filepath, {
              filepath: task.filepath,
              dir_path: task.dir_path,
              status: 'indexing',
              updated_at: Date.now(),
              git_url: task.git_url
            });
          }
        }
        this.logger.reportUserAction({
          key: 'kwapilot-binary-' + INDEX_MANAGER_NAMESPACE,
          subType: 'executing_index_tasks_per_batch',
          content: JSON.stringify({
            dirPath,
            time: Date.now() - startExecuteTime,
            fileCount: totalCount,
            processedCount: totalProcessed,
            isRepoBuilding: this.isRepoBuilding
          })
        });
        if (!this.shouldCancel) {
          this.logger.debug('Batch completed, triggering garbage collection');
          global.gc?.();
        }
      }
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'executing_index_tasks_cost',
        millis: Date.now() - startExecuteTime,
        extra3: dirPath,
        extra4: totalCount.toString(),
        extra6: this.isRepoBuilding ? 'true' : 'false'
      });
      this.logger.reportUserAction({
        key: 'kwapilot-binary-' + INDEX_MANAGER_NAMESPACE,
        subType: 'executing_index_tasks',
        content: JSON.stringify({
          dirPath,
          time: Date.now() - startExecuteTime,
          fileCount: totalCount,
          processedCount: totalProcessed,
          isRepoBuilding: this.isRepoBuilding
        })
      });
      this.logger.info('Optimizing database table');
      await LanceDB.optimizeTable(generateCollectionName(dirPath));
      this.logger.info('Database table optimization completed');
    } catch (error) {
      const errorDetails = {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        code: (error as any)?.code,
        type: error?.constructor?.name,
        dirPath: this.dirPath,
        status: this.status,
        isExecuting: this.isExecuting,
        totalCount
      };

      this.logger.error('Error executing index tasks---2222:', {
        error: errorDetails,
        context: 'executeIndexTasks',
        timestamp: new Date().toISOString()
      });

      // Report error metrics
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'execute_index_tasks_error',
        millis: Date.now(),
        extra3: errorDetails.type,
        extra4: errorDetails.message,
        extra5: this.dirPath,
        extra6: JSON.stringify(errorDetails)
      });
    } finally {
      this.isExecuting = false;
      if (this.isRepoBuilding) {
        this.isRepoBuilding = false;
        this.logger.info('Repo building completed');
      }
      if (!this.shouldCancel) {
        await this.updateProgress(dirPath, totalCount, totalCount, true, '');
      }
      this.logger.info('executeIndexTasks: Execution completed');
    }
  }
  async processFile(filePath: string, dirPath: string, action: IndexTaskAction, gitUrl: string): Promise<void> {
    try {
      // 使用正则表达式检查文件路径是否包含隐藏目录
      const hiddenDirPattern = /(\/|^)\.\w+/; // 匹配以 `.` 开头的目录或文件
      if (hiddenDirPattern.test(filePath)) {
        this.logger.info(`processFile: ${filePath} is in hidden directory, skipping`);
        return;
      }

      this.logger.info(`processFile: ${filePath}`);
      // 由于on事件不会等待 TaskTable.create 完成，所以需要先判断是否已经存在任务
      // if (this.recentProcessedFiles.includes(filePath)) {
      //   this.logger.info(`processFile: ${filePath} already processed`);
      //   return;
      // }
      // this.recentProcessedFiles.push(filePath);

      const existingTask = await TaskTable.findByFilePath(filePath);
      if (existingTask) {
        this.logger.info(`processFile: ${filePath} already exists`);
        return;
      }

      const fileStatus = await FileStatusTable.findByFilePath(filePath);
      // 读取文件并计算hash
      // todo: 已经删除的文件无法进行 hash，这种要如何处理
      if (!fs.existsSync(path.resolve(dirPath, filePath))) {
        await this.addTask(filePath, dirPath, action, gitUrl);
        return;
      }
      const hash = await this.fileSystem.getFileHash(path.resolve(dirPath, filePath));
      if (fileStatus?.hash === hash) {
        if (fileStatus.status === 'indexed') {
          this.logger.info(`processFile: ${filePath} already indexed`);
          return;
        }
        if (fileStatus.status === 'indexing') {
          this.logger.info(`processFile: ${filePath} already indexing`);
          return;
        }
      }
      this.logger.info(`processFile: ${filePath} add task`);
      await this.addTask(filePath, dirPath, action, gitUrl);
    } catch (error) {
      this.logger.error(`Error in processFile for ${filePath}:`, error);
    }
  }
  async checkFileCountValid(options: RepoIndexOptions, validFiles: number) {
    if (validFiles > MAX_INDEX_FILE_COUNT) {
      this.logger.error(
        `processRepo: ${options.dirPath} validFiles: ${validFiles}, 可以索引的文件数量超过 ${MAX_INDEX_FILE_COUNT}`
      );
      this.logger.perf({
        namespace: AGENT_NAMESPACE,
        subtag: 'process_repo_over_10k',
        millis: 1,
        extra3: validFiles.toString(),
        extra6: options.gitUrl
      });
      await RepoStatusTable.update(options.dirPath, {
        is_building: false,
        status: RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K
      });
      this.logger.reportUserAction({
        key: 'process_repo_over_10k',
        type: 'process_repo_over_10k',
        subType: options.gitUrl,
        content: JSON.stringify({
          repo: options.gitUrl,
          dirPath: options.dirPath
        })
      });
      await TaskTable.deleteByDirPath(options.dirPath);
      return false;
    } else {
      return true;
    }
  }

  async processRepo(options: RepoIndexOptions) {
    try {
      this.logger.info(`processRepo: ${options.dirPath}`);
      try {
        await ConcurrencyLock.acquireLock(options.dirPath);
      } catch (error) {
        this.logger.error(`processRepo: ${options.dirPath} is locked by other instance, skipping`);
        this.startExecute();
        return;
      }

      let repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
      this.shouldCancel = false;
      this.isRepoBuilding = true;
      // check git status with kconf
      const isGitRepo = await this.gitManager.isGitRepo(options.dirPath);
      if (!isGitRepo) {
        await this.sendProgress(0, 100, 1, '', IndexAction.ERROR, RepoStatusMessageMap[RepoStatusEnum.NOT_GIT_REPO]);
        this.logger.info(`processRepo: ${options.dirPath} is not a git repo, skipping`);
        return;
      }
      await this.sendProgress(0.01, 100, 1, '', IndexAction.PROCESS_FILE, '开始索引项目');
      // 判断是否为 git 仓库名单
      const gitSshUrl = convertToGitSshFormat(options.gitUrl);
      let repoCacheInfo: { commitId: string; branch: string } | null = null;
      if (!gitSshUrl) {
        this.logger.warn(`Failed to convert repo URL to git SSH format: ${gitSshUrl}`);
      } else {
        repoCacheInfo = (await this.httpClient.getLargeRepoIndexConfig(gitSshUrl)) as {
          commitId: string;
          branch: string;
        };
      }
      if (repoStatus?.status === RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_5K) {
        // 如果之前是超过 5k的，则重新处理一遍。
        await this.clearIndex(options.dirPath);
        repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
      }
      if (
        repoCacheInfo &&
        (!repoStatus ||
          repoStatus.status === RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K ||
          repoStatus.status === RepoStatusEnum.LARGE_REPO_INIT)
      ) {
        this.logger.perf({
          namespace: INDEX_MANAGER_NAMESPACE,
          subtag: 'process_repo_use_remote_index',
          millis: Date.now(),
          extra3: options.dirPath,
          extra4: options.gitUrl,
          extra6: repoStatus ? 'update' : 'create'
        });
        if (!repoStatus) {
          await RepoStatusTable.create({
            dir_path: options.dirPath,
            commit_id: repoCacheInfo?.commitId,
            base_commit_id: repoCacheInfo?.commitId || undefined,
            created_at: Date.now(),
            updated_at: Date.now(),
            git_url: options.gitUrl,
            ide_version: options.ideVersion,
            plugin_version: options.pluginVersion,
            platform: options.platform as IdePlatform,
            total_files: 0,
            done_files: 0,
            is_building: true,
            status: RepoStatusEnum.LARGE_REPO_INIT,
            using_remote_index: false
          });
          repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
        } else {
          // 如果 basecommit 更新，那么该如何处理呢？
          // 使用git rev-list --ancestry-path a1b2c3d4..e5f6g7h8 排查两个 commit 的先后顺序
          // 移除掉两个 commit 之间的文件变更（diff 当前 commitid 和 basecommitId 之间的文件变更，遍历数据库中的文件，如果不在变更列表中，则删除。）
          // 文件删除之前记录状态，防止出现状态改变但是未删除完的情况，用户关闭 ide
          await RepoStatusTable.update(options.dirPath, {
            status: RepoStatusEnum.LARGE_REPO_INIT,
            base_commit_id: repoCacheInfo?.commitId || undefined
          });
          repoStatus = await RepoStatusTable.findByDirPath(options.dirPath);
        }
      }
      if (!repoStatus) {
        if (!GlobalConfig.getConfig().getIsDBImgrated()) {
          GlobalConfig.getConfig().setIsDBImgrated(true);
          await SqliteDb.resetDBConnection();
        }
        try {
          // 插入项目结构
          const collectionName = generateCollectionName(options.dirPath);
          const embedding = new CodeEmbedding(collectionName);
          await embedding.insertRepoStructure(options.dirPath);
        } catch (e) {
          this.logger.error(`processRepo: ${options.dirPath} error inserting repo structure: ${e}`);
        }
        const allFiles = await this.gitManager.getAllFiles(options.dirPath);
        const totalFiles = allFiles.length;
        await RepoStatusTable.create({
          dir_path: options.dirPath,
          commit_id: options.commitId,
          base_commit_id: repoCacheInfo?.commitId || undefined,
          created_at: Date.now(),
          updated_at: Date.now(),
          git_url: options.gitUrl,
          ide_version: options.ideVersion,
          plugin_version: options.pluginVersion,
          platform: options.platform as IdePlatform,
          total_files: totalFiles,
          done_files: 0,
          is_building: true,
          status: RepoStatusEnum.SUCCESS,
          using_remote_index: false
        });
        let validFiles = 0;
        let doneFiles = 0;
        const BATCH_SIZE = 200; // 减小批次大小
        const DELAY_BETWEEN_BATCHES = 100; // 批次间延迟(ms)
        const MAX_TASKS_IN_MEMORY = 1000; // 内存中最大任务数量
        let validTasks = [];

        // Process files in batches
        for (let i = 0; i < allFiles.length; i += BATCH_SIZE) {
          if (this.shouldCancel) {
            this.logger.info('File processing cancelled');
            break;
          }
          const batchFiles = allFiles.slice(i, Math.min(i + BATCH_SIZE, allFiles.length));

          // 并行处理当前批次的文件
          const batchResults = await Promise.all(
            batchFiles.map(async (filePath) => {
              doneFiles++;

              // 详细检查
              if (!(await this.checkIfNeedAddTask(filePath, options.dirPath))) {
                return null;
              }

              return {
                task_id: nanoid(),
                filepath: filePath,
                dir_path: options.dirPath,
                git_url: options.gitUrl,
                created_at: Date.now(),
                updated_at: Date.now(),
                status: 'pending' as const,
                file_action: 'create' as const,
                retry_count: 0
              };
            })
          );

          // 过滤出有效的任务
          const newValidTasks = batchResults.filter((task): task is NonNullable<typeof task> => task !== null);
          validTasks.push(...newValidTasks);

          // 如果累积的任务达到阈值，批量创建并清空
          if (validTasks.length >= MAX_TASKS_IN_MEMORY) {
            await TaskTable.batchCreate(validTasks);
            validFiles += validTasks.length;
            validTasks = [];
          }

          // 添加延迟，避免系统过载
          await new Promise((resolve) => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
        }

        // 处理剩余的任务
        if (validTasks.length > 0) {
          validFiles += validTasks.length;
          await TaskTable.batchCreate(validTasks);
        }
        const valid = await this.checkFileCountValid(options, validFiles);
        if (!valid) {
          await this.sendProgress(
            0,
            100,
            1,
            '',
            IndexAction.ERROR,
            `${RepoStatusMessageMap[RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K]}, 当前文件数量: ${validFiles}`
          );
          return;
        }

        await RepoStatusTable.update(options.dirPath, {
          is_building: true,
          total_files: validFiles,
          status: RepoStatusEnum.SUCCESS
        });
      } else if (repoStatus.commit_id !== options.commitId) {
        // 判断如果找不到 commitId，提示用户执行 git pull
        let changedFiles: Array<{ path: string; status: IndexTaskAction }> = [];
        try {
          changedFiles = await this.gitManager.getChangedFilesBetweenCommits(
            options.dirPath,
            options.commitId,
            repoStatus.commit_id || repoCacheInfo?.commitId || ''
          );
          this.logger.perf({
            namespace: INDEX_MANAGER_NAMESPACE,
            subtag: 'process_repo_get_changed_files',
            millis: Date.now(),
            extra3: options.dirPath,
            extra4: options.gitUrl,
            extra6: changedFiles.length.toString()
          });
        } catch (error) {
          // 如果当前设置的 commitId 与 base 中的 commitid 一致，并且执行报错了，那么认为是本地无法获取到最新 commitid 信息，提示用户执行 git pull 命令
          if (repoCacheInfo?.commitId && repoStatus.commit_id === repoCacheInfo.commitId) {
            this.logger.error(`processRepo: ${options.dirPath} error getting changed files: ${error}`);
            await this.sendProgress(0, 100, 1, '', IndexAction.ERROR, 'git 仓库异常，无法拉取到最新 commit信息');
            this.messenger.send('state/sendNotification', {
              type: 'warning',
              name: 'GIT_DIFF_ERROR',
              message: `索引更新异常，当前无法拉取到最新commit信息，请执行 git pull 命令`
            });
            // 上报
            this.logger.perf({
              namespace: INDEX_MANAGER_NAMESPACE,
              subtag: 'git_diff_error',
              millis: Date.now(),
              extra6: `repoStatus.commit_id: ${repoStatus.commit_id},options.commitId: ${options.commitId},repoCacheInfo?.commitId: ${repoCacheInfo?.commitId}`
            });
            this.startExecute();
            return;
          }
        }

        let doneFiles = 0;
        for (const { path: filePath, status } of changedFiles) {
          await this.processFile(filePath, options.dirPath, status, options.gitUrl);
          doneFiles++;
        }

        // 获取当前进度
        const indexedLen = await FileStatusTable.countByDirPath(options.dirPath);
        const pendingTasks = await TaskTable.findByStatus(options.dirPath, 'pending');
        const indexingTasks = await TaskTable.findByStatus(options.dirPath, 'indexing');
        const totalFiles = pendingTasks.length + indexingTasks.length + (indexedLen ?? 0);

        await RepoStatusTable.update(options.dirPath, {
          commit_id: options.commitId,
          updated_at: Date.now(),
          ide_version: options.ideVersion,
          plugin_version: options.pluginVersion,
          platform: options.platform as IdePlatform,
          total_files: totalFiles,
          done_files: indexedLen,
          is_building: true
        });
      }
      if (repoStatus && repoStatus?.status === RepoStatusEnum.MAX_INDEX_SIZE_EXCEEDED_10K) {
        this.logger.info(`processRepo: ${options.dirPath} repo status is 1, skipping`);
        this.logger.perf({
          namespace: INDEX_MANAGER_NAMESPACE,
          subtag: 'process_repo_over_10k',
          millis: 1,
          extra3: options.dirPath,
          extra6: options.gitUrl
        });
        this.logger.reportUserAction({
          key: 'process_repo_over_10k',
          type: 'process_repo_over_10k',
          subType: options.gitUrl,
          content: JSON.stringify({
            repo: options.gitUrl,
            dirPath: options.dirPath
          })
        });
        return;
      }

      this.startExecute();
    } catch (error) {
      const errorDetails = {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        code: (error as any)?.code,
        type: error?.constructor?.name,
        dirPath: options?.dirPath,
        gitUrl: options?.gitUrl,
        commitId: options?.commitId,
        platform: options?.platform
      };

      this.logger.error('Error in processRepo:', {
        error: errorDetails,
        context: 'processRepo',
        timestamp: new Date().toISOString()
      });

      // Report error metrics
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'process_repo_error',
        millis: Date.now(),
        extra3: errorDetails.type,
        extra4: errorDetails.message,
        extra5: options?.dirPath,
        extra6: JSON.stringify(errorDetails)
      });
    }
  }

  public async getRepoBuildStatus(): Promise<{
    id: number;
    repo: string;
    repoPath: string;
    branch: string;
    commitId: string;
    lastUpdateTime: number;
    createTime: number;
    total: number;
    done: number;
    progress: number;
    isBuilding: boolean;
    status: RepoStatusEnum;
    message: string;
    isPaused: boolean;
  } | null> {
    try {
      const [repoStatus, userStatus] = await Promise.all([
        RepoStatusTable.findByDirPath(this.dirPath),
        UserStatusTable.findByDirPath(this.dirPath)
      ]);
      if (!repoStatus) {
        return null;
      }

      return {
        id: repoStatus.id,
        repo: repoStatus.git_url,
        repoPath: repoStatus.dir_path,
        branch: repoStatus.branch ?? '',
        commitId: repoStatus.commit_id,
        lastUpdateTime: repoStatus.updated_at,
        createTime: repoStatus.created_at,
        total: repoStatus.total_files,
        done: repoStatus.done_files,
        progress: repoStatus.total_files > 0 ? repoStatus.done_files / repoStatus.total_files : 1,
        isBuilding: repoStatus.is_building,
        status: repoStatus.status,
        isPaused: userStatus?.status === 0,
        message: RepoStatusMessageMap[repoStatus.status]
      };
    } catch (error) {
      const errorDetails = {
        message: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        code: (error as any)?.code,
        type: error?.constructor?.name,
        dirPath: this.dirPath
      };

      this.logger.error('Error in getRepoBuildStatus:', {
        error: errorDetails,
        context: 'getRepoBuildStatus',
        timestamp: new Date().toISOString()
      });

      // Report error metrics
      this.logger.perf({
        namespace: INDEX_MANAGER_NAMESPACE,
        subtag: 'get_repo_build_status_error',
        millis: Date.now(),
        extra3: errorDetails.type,
        extra4: errorDetails.message,
        extra5: this.dirPath,
        extra6: JSON.stringify(errorDetails)
      });

      return null;
    }
  }

  public async clearIndex(dirPath?: string, isForce: boolean = false) {
    if (isForce) {
      // 删除 index.sqlite 文件
      if (!dirPath) {
        return false;
      }
      try {
        const sqliteDir = getSqlitePath(dirPath);
        if (fs.existsSync(sqliteDir)) {
          await fsPromises.rm(sqliteDir, { recursive: true, force: true });
        }
        this.logger.info(`Deleted sqlite file: ${sqliteDir}`);
      } catch (e) {
        this.logger.warn(`Failed to delete sqlite file for ${dirPath}:`, e);
      }

      // 删除 lancedb 目录
      try {
        const lanceDbRoot = getLanceDbPath();
        const collectionName = generateCollectionName(dirPath);
        const cacheCollectionName = getCacheCollectionName(collectionName);
        const lanceDbDir = path.join(lanceDbRoot, collectionName) + '.lance';
        const cacheLanceDbDir = path.join(lanceDbRoot, cacheCollectionName) + '.lance';
        if (fs.existsSync(lanceDbDir)) {
          await fsPromises.rm(lanceDbDir, { recursive: true, force: true });
        }
        if (fs.existsSync(cacheLanceDbDir)) {
          await fsPromises.rm(cacheLanceDbDir, { recursive: true, force: true });
        }
        this.logger.info(`Deleted lancedb dir: ${lanceDbDir}`);
      } catch (e) {
        this.logger.warn(`Failed to delete lancedb dir for ${dirPath}:`, e);
      }
      return true;
    }

    try {
      this.logger.info(`Clearing index for ${dirPath}`);
      if (!dirPath) {
        dirPath = this.dirPath;
      }
      this.shouldCancel = true;
      // 清除数据库中的记录
      await TaskTable.deleteByDirPath(dirPath);
      await FileStatusTable.deleteByDirPath(dirPath);
      await RepoStatusTable.deleteByDirPath(dirPath);

      // 完全清除所有缓存
      for (const [key] of this.fileHashCache.entries()) {
        this.fileHashCache.delete(key);
      }
      for (const [key] of this.indexCache.entries()) {
        this.indexCache.delete(key);
      }

      // this.recentProcessedFiles = [];
      // 重置状态
      this.isRepoBuilding = false;
      return await clearIndex(dirPath);
    } catch (error) {
      this.logger.error('Error in clearIndex:', error);
      return false;
    }
  }
  public async dispose() {
    try {
      if (this.autoExecuteTimer) {
        clearTimeout(this.autoExecuteTimer);
        this.autoExecuteTimer = null;
      }
      this.status = 'paused';
    } catch (error) {
      this.logger.error('Error in dispose:', error);
    }
  }

  public async initEmbeddingList() {
    try {
      const BATCH_SIZE = 200;
      let offset = 0;
      let isFirstBatch = true;
      const filename = path.join(this.dirPath, 'embedding_list.txt');
      while (true) {
        const batch = await FileStatusTable.findByDirPathPaged(this.dirPath, BATCH_SIZE, offset);
        if (!batch || batch.length === 0) break;
        const fileList = batch.map((f) => f.filepath).join('\n');
        if (isFirstBatch) {
          await fs.promises.writeFile(filename, fileList + '\n', 'utf-8');
          isFirstBatch = false;
        } else {
          await fs.promises.appendFile(filename, fileList + '\n', 'utf-8');
        }
        if (batch.length < BATCH_SIZE) break;
        offset += BATCH_SIZE;
      }
    } catch (error) {
      this.logger.error('Error in initEmbeddingList:', error);
    }
  }
}
