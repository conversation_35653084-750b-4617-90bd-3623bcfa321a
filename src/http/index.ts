import { Logger } from '@/util/log';
import {
  CloudCodeSearchBody,
  CloudCodeSearchResponse,
  CodeRerankBody,
  CodeRerankResponse,
  HttpClientResponse,
  QueryEmbeddingBody,
  QueryEmbeddingResponse,
  IndexingSearchBody,
  IndexingSearchResponse,
  IndexUserFilesResponse
} from '@/http/types';
import { AGENT_NAMESPACE } from '@/util/const';
import { fetchEventSource, FetchEventSourceInit } from '@fortaine/fetch-event-source';
import { GlobalConfig } from '@/util/global';
import * as fs from 'fs';
import path from 'path';
import { convertToGitSshFormat } from '@/util/git-utils';

interface RetryConfig {
  maxRetries?: number; // 最大重试次数，默认 3
  retryDelay?: number; // 重试延迟时间（毫秒），默认 1000
  retryCondition?: (error: any, response?: Response) => boolean; // 重试条件判断函数
  onRetry?: (attempt: number, error: any) => void; // 重试回调函数
}

interface HttpClientOptions {
  config?: RequestInit;
  requestInterceptors?: ((param: any) => any)[];
  responseInterceptors?: ((param: any) => any)[];
  retryConfig?: RetryConfig;
}

class HttpClient {
  private _config: RequestInit = {};
  private requestInterceptors: ((param: any) => any)[] = [];
  private responseInterceptors: ((param: any) => any)[] = [];
  private retryConfig: RetryConfig;
  logger = new Logger('http-client');

  get _baseUrl() {
    return GlobalConfig.getConfig().getKwaiPilotDomain();
  }

  constructor(options?: HttpClientOptions) {
    this._config = options?.config || {};
    this.requestInterceptors = options?.requestInterceptors || [];
    this.responseInterceptors = options?.responseInterceptors || [];

    // 设置默认重试配置
    this.retryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      retryCondition: (error: any, response?: Response) => {
        // 默认重试条件：网络错误或 5xx 服务器错误
        if (error && error.name === 'TypeError') return true; // 网络错误
        if (response && response.status >= 500) return true; // 服务器错误
        return false;
      },
      onRetry: (attempt: number, error: any) => {
        this.logger.warn(`请求重试中，第 ${attempt} 次尝试:`, error);
      },
      ...options?.retryConfig
    };
  }

  async request<R>(url: string, requestInit?: RequestInit) {
    const response = await this.rawRequest(url, requestInit);
    let responseData = (await response.json()) as R;
    this.responseInterceptors.forEach((interceptor) => {
      responseData = interceptor(responseData);
    });

    this.logger.debug('api response after interceptor');
    return responseData;
  }

  async rawRequest(url: string, requestInit?: RequestInit) {
    let config = this.mergeConfig(this._config, requestInit);

    // 只有在没有明确设置 Content-Type 且 body 不是 FormData 时才设置默认的 application/json
    if ((config.headers || !config.headers?.['Content-Type']) && !(config.body instanceof FormData)) {
      config.headers = {
        'Content-Type': 'application/json',
        ...config.headers
      };
    } else {
      config.headers = {
        ...config.headers
      };
    }

    config.method = config.method || 'GET';

    let u = new URL(url, this._baseUrl);
    // qa环境下，这俩接口要走pre环境，因为服务端说qa缺少模型，不支持这俩接口
    if (
      this._baseUrl.includes('qa-kinsight.staging.kuaishou.com') &&
      (url.includes('local-agent-code-embedding') || url.includes('local-agent-code-rerank'))
    ) {
      u = new URL(url, 'https://pre-kinsight.test.gifshow.com/');
    }
    // if (url.includes('nodeapi')) {
    //   u = new URL(url, 'http://localhost:3001');
    // }

    this.logger.debug(`api request url: ${u.toString()}`);
    this.requestInterceptors.forEach((interceptor) => {
      config = interceptor(config);
    });
    this.logger.debug('api request after interceptor');

    // 实现重试逻辑
    let lastError: any;
    let response: Response | undefined;

    for (let attempt = 0; attempt <= this.retryConfig.maxRetries!; attempt++) {
      try {
        response = await fetch(u, config);

        // 检查响应是否成功
        if (response.ok) {
          this.logger.info(`api request success: ${config.method} ${u.pathname} ${response.status}`);
          return response;
        }

        // 检查是否需要重试
        if (attempt < this.retryConfig.maxRetries! && this.retryConfig.retryCondition!(null, response)) {
          this.retryConfig.onRetry!(attempt + 1, new Error(`HTTP ${response.status}: ${response.statusText}`));
          await this.delay(this.retryConfig.retryDelay! * Math.pow(2, attempt)); // 指数退避
          continue;
        }

        // 不满足重试条件或已达到最大重试次数
        this.logger.error('api request error');
        throw new Error('An error occurred');
      } catch (error: any) {
        lastError = error;

        if (error.type === 'aborted') {
          throw 'user abort';
        }

        // 检查是否需要重试
        if (attempt < this.retryConfig.maxRetries! && this.retryConfig.retryCondition!(error)) {
          this.retryConfig.onRetry!(attempt + 1, error);
          await this.delay(this.retryConfig.retryDelay! * Math.pow(2, attempt)); // 指数退避
          continue;
        }

        // 不满足重试条件或已达到最大重试次数
        this.logger.error('api request error');
        throw new Error(error.toString());
      }
    }

    // 如果执行到这里，说明所有重试都失败了
    throw lastError || new Error('Request failed after all retries');
  }
  async get<T, R = HttpClientResponse<T>>(url: string, params: Record<string, string> = {}, config: RequestInit = {}) {
    const p = this.formatUrl(url, params);
    config.method = 'GET';
    return this.request<R>(p, config);
  }
  async delete<T, R = HttpClientResponse<T>, D extends BodyInit = any>(
    url: string,
    body?: D,
    params: Record<string, string> = {},
    config: RequestInit = {}
  ) {
    const p = this.formatUrl(url, params);
    config.method = 'DELETE';
    if (body) {
      config.body = body;
    }
    return this.request<R>(p, config);
  }

  async post<T, R = HttpClientResponse<T>, D extends BodyInit = any>(url: string, body?: D, config: RequestInit = {}) {
    config.method = 'POST';
    config.body = body;
    return this.request<R>(url, config);
  }
  async postForm<T, R = HttpClientResponse<T>>(url: string, formData: FormData, config: RequestInit = {}) {
    config.method = 'POST';
    config.body = formData;
    // 不设置 Content-Type，让浏览器自动设置 multipart/form-data 边界
    const headers = { ...config.headers };
    delete (headers as any)['Content-Type'];
    config.headers = headers;
    return this.request<R>(url, config);
  }
  private mergeConfig(originConfig: RequestInit = {}, config: RequestInit = {}) {
    return { ...originConfig, ...config };
  }

  private formatUrl(url: string, params: Record<string, string>) {
    let isUrl = true;
    const urlObj = new URL(url, 'http://example.com');
    try {
      new URL(url);
    } catch (error) {
      isUrl = false;
    }

    // 获取现有的搜索参数
    const searchParams = urlObj.searchParams;
    // 遍历新的参数对象，并添加到搜索参数中
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    if (!isUrl) {
      return `${urlObj.pathname}${urlObj.search}`;
    }
    return urlObj.toString();
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
  rawFetchEventSource(url: string, options: FetchEventSourceInit) {
    let u = null;
    if (typeof url === 'string') {
      u = new URL(url, this._baseUrl);
    }
    return fetchEventSource(u ? u.toString() : url, options);
  }
}

export class Api extends HttpClient {
  constructor(config?: HttpClientOptions) {
    super(config);
  }
  async queryEmbedding(body: QueryEmbeddingBody) {
    const startTime = Date.now();
    const { data } = await this.post<QueryEmbeddingResponse>(
      '/eapi/kwaipilot/plugin/code/search/local-agent-code-embedding',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'queryEmbeddingCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  async codeRerank(body: CodeRerankBody) {
    const startTime = Date.now();
    const { data } = await this.post<CodeRerankResponse>(
      '/eapi/kwaipilot/plugin/code/search/local-agent-code-rerank',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'codeRerankCost',
      millis: Date.now() - startTime
    });
    return data;
  }
  async cloudCodeSearch(body: CloudCodeSearchBody) {
    const startTime = Date.now();
    const { data } = await this.post<CloudCodeSearchResponse>(
      '/eapi/kwaipilot/plugin/code/search/generate_prompt',
      JSON.stringify(body)
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'cloudCodeSearchCost',
      millis: Date.now() - startTime
    });
    return data;
  }

  // todo: 这里是一个未实现的接口
  async getKconfValue<T>(key: string): Promise<T> {
    const response = await this.post<T>(
      `/eapi/kwaipilot/plugin/v2/config`,
      JSON.stringify({
        key
      })
    );
    return response.data;
  }
  async checkCloudIndex(username: string) {
    const response = await this.get<{ data: boolean }>(`/nodeapi/indexing/index-config`, { username });
    return response?.data;
  }
  // 获取索引配置
  async getIndexConfig() {
    const { data } = await this.get<{
      'file-index-batch': number;
      'index-request-batch': number;
    }>(`/nodeapi/indexing/index-config/index`);
    return data;
  }
  // 获取大仓库索引配置
  async getLargeRepoIndexConfig(key: string): Promise<{ commitId: string; branch: string } | null> {
    const startTime = Date.now();
    const { data } = await this.get<{ commitId: string; branch: string } | null>(
      `/eapi/kwaipilot/plugin/code/search/large-repo-index-config`,
      { key }
    );
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'getLargeRepoIndexConfigCost',
      millis: Date.now() - startTime
    });
    return data;
  }
  // 获取仓库索引的文件数
  async getRepoIndexFileCount(repoUrl: string) {
    const { data } = await this.get<{ count: number }>(`/nodeapi/indexing/file-index/user/indexedFiles`, {
      repoUrl: convertToGitSshFormat(repoUrl) || '',
      userId: GlobalConfig.getConfig().getUsername(),
      deviceId: GlobalConfig.getConfig().getDeviceId()
    });
    return data;
  }
  // 更新仓库信息
  async updateRepoInfo(body: {
    userId: string;
    repoUrl: string;
    deviceId: string;
    ideVersion: string;
    dirPath: string;
    branch?: string;
    commitId?: string;
    baseCommitId?: string;
  }) {
    const { data } = await this.post<{ success: boolean }>('/nodeapi/indexing/repo/user', JSON.stringify(body));
    return data;
  }
  fetchEventSource(url: string, options: FetchEventSourceInit): Promise<void> {
    return this.rawFetchEventSource(url, options);
  }
  // 索引搜索
  async indexingSearch(body: IndexingSearchBody) {
    const startTime = Date.now();
    const { data } = await this.post<IndexingSearchResponse>('/nodeapi/indexing/search', JSON.stringify(body));
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'indexingSearchCost',
      millis: Date.now() - startTime
    });
    return data;
  }
  // 清除用户索引
  async clearIndexForUser() {
    const { data } = await this.delete<{ success: boolean }>(
      '/nodeapi/indexing/repo/user/all',
      JSON.stringify({
        userId: GlobalConfig.getConfig().getUsername(),
        repoUrl: GlobalConfig.getConfig().getRepoInfo()?.git_url || '',
        deviceId: GlobalConfig.getConfig().getDeviceId()
      })
    );
    return data;
  }

  // 从服务端索引文件
  async indexFiles(
    files: { filepath: string; action: 'modify' | 'delete' | 'create'; content?: string }[],
    dirpath: string
  ): Promise<IndexUserFilesResponse> {
    const startTime = Date.now();
    const gitRepo = GlobalConfig.getConfig().getRepoInfo()?.git_url || '';
    let processedRepoId = gitRepo;
    if (gitRepo.startsWith('/')) {
      // 绝对路径直接使用
      processedRepoId = gitRepo;
      this.logger.debug(`Using absolute path as repo_id: ${processedRepoId}`);
    } else {
      // 认为是git url，转换为SSH格式
      const gitSshUrl = convertToGitSshFormat(gitRepo);
      if (gitSshUrl) {
        processedRepoId = gitSshUrl;
        this.logger.debug(`Converted git URL to SSH format: ${gitRepo} -> ${processedRepoId}`);
      } else {
        // 转换失败时使用原始值
        processedRepoId = gitRepo;
        this.logger.warn(`Failed to convert git URL to SSH format, using original: ${gitRepo}`);
      }
    }
    // 构造 JSON 请求体
    const requestBody = {
      username: GlobalConfig.getConfig().getUsername(),
      repoUrl: processedRepoId || dirpath,
      deviceId: GlobalConfig.getConfig().getDeviceId(),
      dirPath: dirpath,
      files: files.map((file) => {
        const fileData: any = {
          filepath: path.relative(dirpath, file.filepath),
          action: file.action
        };

        // 对于修改或创建操作，读取文件内容
        if (file.action === 'modify' || file.action === 'create') {
          try {
            if (file.content) {
              fileData.content = file.content;
            } else {
              if (fs.existsSync(file.filepath)) {
                const content = fs.readFileSync(file.filepath, 'utf-8');
                fileData.content = content;
                this.logger.debug(`已读取文件内容: ${file.filepath}`);
              } else {
                this.logger.warn(`文件不存在: ${file.filepath}`);
                fileData.content = '';
              }
            }
          } catch (error) {
            this.logger.error(`读取文件失败: ${file.filepath}`, error);
            fileData.content = '';
          }
        }

        return fileData;
      })
    };

    // 使用 post 方法发送 JSON 数据
    const { data } = await this.post<IndexUserFilesResponse>(
      '/nodeapi/indexing/file-index/user',
      JSON.stringify(requestBody)
    ).catch((e) => {
      this.logger.error('Index files failed:', e);
      return { data: {} };
    });
    this.logger.perf({
      namespace: AGENT_NAMESPACE,
      subtag: 'indexFilesCost',
      millis: Date.now() - startTime,
      extra3: files.length.toString(),
      extra4: 'batch_index_json'
    });
    return data as IndexUserFilesResponse;
  }
}
